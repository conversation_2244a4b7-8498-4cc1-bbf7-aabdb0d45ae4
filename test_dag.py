from datetime import timedelta
from datetime import datetime
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.python import BranchPythonOperator
from airflow.api.common.experimental.pool import get_pool, create_pool
from airflow.exceptions import PoolNotFound
from airflow.utils.dates import days_ago
from airflow.models import Variable
from airflow.utils.email import send_email
from pathlib import Path
from kubernetes.client import models as k8s
import json
import csv
import logging
import socket
import time
import math
import os
import sys
import base64
import subprocess
import requests
from os.path import expanduser
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
from utility.bashUtils import execute_command, read_file, write_to_file, get_week_number, get_subscribers, get_google_sheet_data


log = logging.getLogger(__name__)

PARENT_DAG_NAME = 'test_dag'

email_group = get_subscribers(PARENT_DAG_NAME)

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email': email_group,
    'email_on_failure': True,
    'email_on_retry': False,
    'retries':1 ,
    'retry_delay': timedelta(seconds=30),
}

dag = DAG(
    PARENT_DAG_NAME,
    default_args=default_args,
    description='DAG to send mail for monnthly library Update',
    schedule_interval='5 4 1 * *', #“At 05:30 every month on date 1”
    max_active_runs=1,
    catchup = False,
    start_date=days_ago(10),
    tags= ['monthly']
)

client_secret_mount_path = '/home/<USER>/google-client-secret'
consul_read_token_mount_path = '/home/<USER>/consul_read_token'
consul_endpoint='prod-consul.zl.internal'
zl_services_endpoint = "ops-zl-services-meta.strawmine.com"
# zl_services_endpoint = "alpha-zl-services-meta.strawmine.com"
dependency_api = "api/project/dependencies/update/direct"

def get_value_from_consul(consul_uri,consul_read_token):
    headers = {'X-Consul-Token': consul_read_token}
    MAX_TRIES = 3
    tries = 0
    resp = None
    while True:
        resp = requests.get(consul_uri,headers=headers)
        if resp.status_code != 200 and tries < MAX_TRIES:
            tries += 1
            continue
        break
    return resp.json()

def get_all_projects_from_consul(consul_endpoint,consul_read_token):
    consul_uri = f"http://{consul_endpoint}/v1/kv/admin/play-projects?keys"
    all_keys = get_value_from_consul(consul_uri,consul_read_token)
    # removing the folder key
    if 'admin/play-projects/' in all_keys: all_keys.remove('admin/play-projects/')
    all_projects = map(lambda keys:  keys.split("/")[2], all_keys)
    return list(all_projects)

def get_project_to_service_mapping(consul_endpoint,consul_read_token):
    consul_uri = f"http://{consul_endpoint}/v1/kv/admin/jenkins?recurse"
    resp  = get_value_from_consul(consul_uri, consul_read_token)
    return resp

def get_dependency_mapping_from_zl_services(zl_services_uri,payload):
    headers = {'Content-Type': 'application/json'}
    MAX_TRIES = 0
    tries = 0
    resp = None
    while True:
        resp = requests.post(zl_services_uri, data=payload, headers=headers)
        if resp.status_code != 200 and tries < MAX_TRIES:
            tries += 1
            continue
        break
    return resp.json()

def get_dependency_and_mail(**kwargs):
    # Overriding with variable project if passed in dag run {"project": "zilingo-userside"}. Fallback to all
    to = kwargs['dag_run'].conf.get('sending-to','<EMAIL>')
    send_email(to, "test-email", "test-body")


    



get_all_rds_list = PythonOperator(
    task_id='get_update_and_mail',
    dag=dag,
    python_callable=get_dependency_and_mail,
    executor_config={
            "pod_override": k8s.V1Pod(
                spec=k8s.V1PodSpec(
                    containers=[
                        k8s.V1Container(
                            name="base",
                            volume_mounts=[
                                k8s.V1VolumeMount(
                                    mount_path=client_secret_mount_path, name="google-client-secret"
                                ),
                                k8s.V1VolumeMount(
                                    mount_path=consul_read_token_mount_path, name="consul-read-token"
                                )
                            ],
                        )
                    ],
                    volumes=[
                        k8s.V1Volume(
                            name="google-client-secret",
                            secret=k8s.V1SecretVolumeSource(secret_name="google-client-secret"),
                        ),
                        k8s.V1Volume(
                            name="consul-read-token",
                            secret=k8s.V1SecretVolumeSource(secret_name="consul-read-token"),
                        )
                    ]
                )
            )
    }
)
get_all_rds_list