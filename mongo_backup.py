# Main DAG File for taking mongo backup
# Pipeline is as follows : lock mongo for writes -> create ebs snapshot -> verify if snapshot is completed ->
# unlock mongo -> delete older snapshots
# limitation of this pipeline:
# Allowed values for times is a list with integer range 0, 23

from airflow import DAG
from airflow.operators.python import <PERSON><PERSON><PERSON><PERSON><PERSON>perator, PythonOperator
from airflow.utils.dates import days_ago
from airflow.models import Variable, TaskInstance
from airflow.utils.email import send_email
from airflow.utils import timezone
from airflow.exceptions import AirflowSkipException
from airflow.api.common.experimental.get_task_instance import get_task_instance
from kubernetes.client import models as k8s
from datetime import datetime, timedelta
import logging
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
from utility.bashUtils import execute_command, write_to_file, get_subscribers

log = logging.getLogger(__name__)

PARENT_DAG_NAME = "BACKUP_MONGO"

email_group = get_subscribers(PARENT_DAG_NAME)

time_interval = 30 #dag runs every 30 minutes

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email': email_group,
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(seconds=60)
}

dag = DAG(
    PARENT_DAG_NAME,
    default_args=default_args,
    description='Dag to take backup of each mongo nodes through aws snapshot',
    schedule_interval=timedelta(minutes=time_interval),
    catchup=False,
    max_active_runs=1,
    start_date=days_ago(2),
    tags= ['prod', 'backup']
)
 

def _select_mongo_nodes(**kwargs):
    current_hour = datetime.utcnow().time().hour
    last_execution_date = Variable.get('last_mongo_backups_execution')
    selected_tasks = []

    for obj in mongo_list['ebs_snapshot']:
        name = obj['name']
        times = obj['times']
        task_to_check='create_snapshot_{}'.format(name)

        if last_execution_date:
            last_execution_date = timezone.convert_to_utc(datetime.strptime(str(last_execution_date), '%Y-%m-%dT%H:%M:%S.%f+00:00'))
            try:
                last_task_instance = get_task_instance(PARENT_DAG_NAME, task_to_check, last_execution_date)
            except:
                log.info("Task instance for execution time {} not found".format(last_execution_date))
                if current_hour in times:
                    selected_tasks.append('lock_mongo_{}'.format(name))
                continue
            if last_task_instance:
                last_task_status = last_task_instance.state
                last_start_date = last_execution_date + timedelta(minutes=time_interval)
                previous_task_hour = last_start_date.strftime('%-H')

                #conditions for skipping
                #This particular dag expects the time minimum of 1 hour
                #skip the snapshot if not required as per the give times, the array which specifies the time hour during which snapshot needs to be taken and when last task was a success
                if current_hour not in times and "failed" not in last_task_status:
                    log.info("Out of schedule time and last job is not failure. Skipping..")
                    continue 

                #Skip the snapshot if snapshot is already successful for the given hour
                if str(current_hour) == previous_task_hour and "failed" not in last_task_status:
                    log.info("one of the run in current hour is success, skipping...")
                    continue
        else:
            if current_hour not in times:
                log.info("Out of schedule time. skipping..")
                continue

        selected_tasks.append('lock_mongo_{}'.format(name))

    #set the current dag execution time for next run
    Variable.set("last_mongo_backups_execution", str(kwargs['execution_date']))

    return selected_tasks


def _create_snapshot(**kwargs):
    name =  kwargs['name']
    hostname = kwargs['hostname']
    volume_id = kwargs['volume_id']
    environment = kwargs['environment']
    mongo_backup_assume_role = mongo_backup_assume_roles[environment] if environment in mongo_backup_assume_roles.keys() else "None"
    snapshot_name = name + "-" + hostname + "-mongodb-" + datetime.utcnow().strftime('%Y-%m-%d-%H')
    snapshot_description = environment + " " + name + " mongo backup"
    log.info("Executing script for creating snapshot")
    execute_command(["bash", "/opt/airflow/dags/scripts/mongo-snapshot/create_mongo_snapshot.sh",
                     volume_id, snapshot_name, snapshot_description, environment, mongo_backup_assume_role], shell=False)


def _delete_old_snapshots(**kwargs):
    volume_id = kwargs['volume_id']
    environment = kwargs['environment']
    mongo_backup_assume_role = mongo_backup_assume_roles[environment] if environment in mongo_backup_assume_roles.keys() else "None"
    log.info("Executing script for deleting old mongo snapshots")
    execute_command(["bash", "/opt/airflow/dags/scripts/mongo-snapshot/delete_mongo_snapshots.sh",
                    volume_id, mongo_backup_assume_role], shell=False)


def _lock_or_unlock_mongo(**kwargs):
    name =  kwargs['name']
    ip = kwargs['ip']
    port = kwargs['port']
    action = kwargs['action']
    avoid_mongo_lock = kwargs['avoid_mongo_lock']
    if action == "unlock-mongo":
        dag_instance = kwargs['dag']
        execution_date = kwargs['execution_date']
        lock_mongo_task = "lock_mongo_{}".format(name)
        operator_instance = dag_instance.get_task(lock_mongo_task)
        task_status = TaskInstance(operator_instance, execution_date).current_state()
        if task_status != "success":
            raise AirflowSkipException(f'skipping the task because lock mongo is failure')
            sys.exit(0)
    if avoid_mongo_lock != "true":
        log.info("Executing script for {}".format(action))
        execute_command(["bash", "/opt/airflow/dags/scripts/mongo-snapshot/lock_or_unlock_mongo.sh",
                    ip, port, action], shell=False)


select_mongo_nodes = BranchPythonOperator(
    task_id='select_mongo_nodes',
    dag=dag,
    provide_context=True,
    python_callable=_select_mongo_nodes
)


mongo_list = Variable.get('mongo_list', deserialize_json=True)
mongo_backup_assume_roles = mongo_list['mongo_backup_assume_roles']

for obj in mongo_list['ebs_snapshot']:
    ip = obj['ip']
    name = obj['name']
    hostname = obj['hostname']
    port = obj['port']
    volume_id = obj['volumeid']
    environment = obj['environment']
    avoid_mongo_lock = obj['avoid_mongo_lock'] if 'avoid_mongo_lock' in obj.keys() else "false"

    create_snapshot = PythonOperator(
        task_id='create_snapshot_{}'.format(name),
        dag=dag,
        python_callable=_create_snapshot,
        op_kwargs={'name': name, 'hostname': hostname, 'volume_id': volume_id, 'environment': environment},
        executor_config={
            "pod_override": k8s.V1Pod(
                spec=k8s.V1PodSpec(
                    service_account_name="aws-airflow-mongo-backup",
                    security_context=k8s.V1PodSecurityContext(
                        fs_group=50000, run_as_group=50000, run_as_user=50000
                    ),
                    containers=[
                        k8s.V1Container(
                            name="base",
                        )
                    ],
                )
            )
        }
    )

    lock_mongo = PythonOperator(
        task_id='lock_mongo_{}'.format(name),
        dag=dag,
        python_callable=_lock_or_unlock_mongo,
        op_kwargs={'name': name, 'ip': ip, 'hostname': hostname, 'port': port, "action": "lock-mongo", "avoid_mongo_lock": avoid_mongo_lock},
        executor_config={
            "pod_override": k8s.V1Pod(
                spec=k8s.V1PodSpec(
                    security_context=k8s.V1PodSecurityContext(
                        fs_group=50000, run_as_group=50000, run_as_user=50000
                    ),
                    containers=[
                        k8s.V1Container(
                            name="base",
                            volume_mounts=[
                                k8s.V1VolumeMount(
                                    mount_path="/home/<USER>/mongo-credentials", name="mongo-secret"
                                )
                            ],
                        )
                    ],
                    volumes=[
                        k8s.V1Volume(
                            name="mongo-secret",
                            secret=k8s.V1SecretVolumeSource(secret_name="mongo-credentials"),
                        ),
                    ],
                )
            )
        }
    )

    unlock_mongo = PythonOperator(
        task_id='unlock_mongo_{}'.format(name),
        dag=dag,
        python_callable=_lock_or_unlock_mongo,
        op_kwargs={'name': name, 'ip': ip, 'hostname': hostname, 'port': port, "action": "unlock-mongo", "avoid_mongo_lock": avoid_mongo_lock},
        trigger_rule='all_done',
        executor_config={
            "pod_override": k8s.V1Pod(
                spec=k8s.V1PodSpec(
                    security_context=k8s.V1PodSecurityContext(
                        fs_group=50000, run_as_group=50000, run_as_user=50000
                    ),
                    containers=[
                        k8s.V1Container(
                            name="base",
                            volume_mounts=[
                                k8s.V1VolumeMount(
                                    mount_path="/home/<USER>/mongo-credentials", name="mongo-secret"
                                )
                            ],
                        )
                    ],
                    volumes=[
                        k8s.V1Volume(
                            name="mongo-secret",
                            secret=k8s.V1SecretVolumeSource(secret_name="mongo-credentials"),
                        ),
                    ],
                )
            )
        }
    )

    snapshot_rotation = PythonOperator(
        task_id='delete_old_snapshots_{}'.format(name),
        dag=dag,
        python_callable=_delete_old_snapshots,
        op_kwargs={'volume_id': volume_id, 'environment': environment},
        executor_config={
            "pod_override": k8s.V1Pod(
                spec=k8s.V1PodSpec(
                    service_account_name="aws-airflow-mongo-backup",
                    security_context=k8s.V1PodSecurityContext(
                        fs_group=50000, run_as_group=50000, run_as_user=50000
                    ),
                    containers=[
                        k8s.V1Container(
                            name="base",
                        )
                    ],
                )
            )
        }
    )     
    
    select_mongo_nodes.set_downstream(lock_mongo)
    lock_mongo.set_downstream(create_snapshot)
    create_snapshot.set_downstream(unlock_mongo)
    create_snapshot.set_downstream(snapshot_rotation)


select_mongo_nodes

