import logging
import subprocess
import math
from datetime import timed<PERSON>ta
from datetime import datetime
import os
import errno
from airflow.models import Variable
from airflow.exceptions import PoolNotFound
from airflow.api.common.experimental.pool import get_pool, create_pool
import gspread


log = logging.getLogger(__name__)


def get_subscribers(dag_name):
    _email_group_json = Variable.get('subscriptions', deserialize_json=True)
    return list(set(_email_group_json['default'] + _email_group_json.get(dag_name,[])))


# Function to create pool in Airflow, if not exists
def _create_pool(name, slots, description):
    try:
        pool = get_pool(name)
        if pool:
            return
    except PoolNotFound:
        pool = create_pool(name, slots, description)


def execute_command(cmd_string, shell=True):
    __output = subprocess.Popen(cmd_string, shell=shell, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    stdout, stderr = __output.communicate()
    log.info("stdout=====> \n {}".format(stdout).replace('\\n', '\n').replace('\\t', '\t'))
    log.info("stderr=====> {}".format(stderr).replace('\\n', '\n').replace('\\t', '\t'))
    if __output.returncode != 0:
        raise Exception(f"Failure while executing command {cmd_string}")
    else:
        log.info(f"Successfully executed command {cmd_string}")


def read_file(file_path):
    with open(file_path, 'r') as _file:
        return _file.read()


# Writes the content to a file.
# Creates the intermediate folders if they do not exist
def write_to_file(file_path, content, append = False):
    accessMode = 'a' if append else 'w'
    if not os.path.exists(os.path.dirname(file_path)):
        try:
            os.makedirs(os.path.dirname(file_path))
        except OSError as exc: # Guard against race condition
            if exc.errno != errno.EEXIST:
                raise
    with open(file_path, accessMode) as _file:
        _file.write(content)


def get_week_number(date,reference_date = datetime(1970,1,4)):
    return math.ceil(int((date.replace(tzinfo=None) - reference_date).days)/7)


# Params: 
            # client_secret: location to client_secret.json file
            # sheet_id: google_sheet id
            # worksheet_names: List of worksheets to be fetched from the Google sheet
# returns:
            # Dictionary with key as worksheet name and value as all_data from the worksheet
# reference: 
            # https://docs.gspread.org/en/latest/user-guide.html
def get_google_sheet_data(client_secret,sheet_id,worksheet_names):
    gc = gspread.service_account(filename=client_secret)
    sh = gc.open_by_key(sheet_id)
    worksheet_data = {}
    for worksheet_name in worksheet_names:
        worksheet = sh.worksheet(worksheet_name)
        all_records = worksheet.get_all_records()
        worksheet_data[worksheet_name] = all_records
    return worksheet_data