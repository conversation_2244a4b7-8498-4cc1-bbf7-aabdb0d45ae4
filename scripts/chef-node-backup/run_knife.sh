#!/bin/bash
set -e
home="/home/<USER>"
chef_config="$home/chef/chef_client.rb"

mkdir -p ${home}/chef/backup/node-data && mkdir -p ${home}/chef/backup/data-bag
cd ${home}/chef/backup

#node data
for item in $(knife node list -c ${chef_config}); do
  knife node show $item -F json -c ${chef_config} > node-data/${item}
done

#data bags
for bag in $(knife data bag list -c ${chef_config}); do
  mkdir ${home}/chef/backup/data-bag/${bag}
  for item in $(knife data bag show $bag -c ${chef_config}); do
    knife data bag show $bag $item -c ${chef_config} > data-bag/${bag}/${item}
  done
done


