#!/bin/bash

source /opt/airflow/dags/scripts/common/utils.sh

volumeid=$1
mongo_backup_assume_role=$2
delete_type='age_basis' # either 'count_basis' or 'age_basis'

exit_on_empty $volumeid

if [ "x$mongo_backup_assume_role" != "xNone" ]; then
  echo "Assumung role"
  eval $(aws sts assume-role --role-arn "$mongo_backup_assume_role" --role-session-name RoleSessionNcinga | jq -r '.Credentials | "export AWS_ACCESS_KEY_ID=\(.AccessKeyId)\nexport AWS_SECRET_ACCESS_KEY=\(.SecretAccessKey)\nexport AWS_SESSION_TOKEN=\(.SessionToken)\n"')
fi

function delete_snapshot(){
    snapshot_id=$1
    echo "deleting $snapshot_id"
    aws ec2 delete-snapshot --region ap-southeast-1 --snapshot-id $snapshot_id
    if [ $? != 0 ]; then
        echo "Failed to delete snapshot ${snapshot_id}"
        exit 1;
    fi
}


if [ $delete_type == 'count_basis' ]; then
    snapshots_to_delete=()
    num_snaps_needed=100 #dummy value
    #get the snapshots
    snapshots=$(aws ec2 describe-snapshots  --region ap-southeast-1 --filters Name=volume-id,Values="$volumeid" Name=tag:airflow_pipeline,Values=mongo_backup Name=tag:created_by_airflow,Values=true --query "sort_by(Snapshots, &StartTime)" --output json | jq -r '.[].SnapshotId')
    readarray -t snapshots_sorted <<<"$snapshots"
    echo "List of snapshots for ${volumeid}: ${snapshots_sorted[*]}"

    #find the snapshots to delete based on num_snaps_needed
    total_snaps=${#snapshots_sorted[@]}
    if [ $total_snaps -gt $num_snaps_needed ]; then
        num_snaps_delete=$[total_snaps - num_snaps_needed]
        snapshots_to_delete=${snapshots_sorted[@]:0:${num_snaps_delete}}
        echo "List of snapshots to delete: ${snapshots_to_delete[*]}"
    fi

    #delete
    for snapshot_id in $snapshots_to_delete; do
        delete_snapshot $snapshot_id
    done
fi


if [ $delete_type == 'age_basis' ]; then
    snapshots=$(aws ec2 describe-snapshots  --region ap-southeast-1 --filters Name=volume-id,Values="$volumeid" Name=tag:airflow_pipeline,Values=mongo_backup Name=tag:created_by_airflow,Values=true --query "sort_by(Snapshots, &StartTime)" --output json | jq -r '.[] | .StartTime + "," + .SnapshotId')
    kept_for_months=()
    keep_for_weeks=()
    cty=$(date +"%Y")
    ctm=$(date +"%-m")
    retention_months=3
    retention_weeks=4
    for snap_details in $snapshots; do
        IFS=, read start_time snapshot_id <<< $snap_details
        echo "snapshot - $snapshot_id"
        scy=$(date -d $start_time '+%Y')
        scm=$(date -d $start_time '+%-m')
        month_difference=$(( ($cty * 12 + $ctm) - ($scy * 12 + $scm) ))
        days_difference=$(( ($(date +%s) - $(date +%s -d $start_time))/86400 ))
        weeks_difference=$(( $days_difference/7 ))

        #Delete if snapshot is older than #{retention_months} months old
        if [ $month_difference -gt $retention_months ]; then
            echo "deleting snapshot (older than 3 months) $start_time $snapshot_id"
            delete_snapshot $snapshot_id
        else
            #Keep only 1 snapshot for snapshots aging #{retention months} months old to 2 month old
            if ! printf '%s\n' "${kept_for_months[@]}" | grep -Pq ^${scm}$; then
               echo "keeping (for monthly backup) $start_time $snapshot_id"
               kept_for_months+=($scm)
            else
               if [ $weeks_difference -gt $retention_weeks ]; then
                  echo "deleting snapshot (less than $retention_months months) $start_time $snapshot_id"
                  delete_snapshot $snapshot_id
                fi
            fi
        fi

        #Keep 1 snapshot for snapshots aging #{retention_weeks} weeks to 7 days old
        if [ $weeks_difference -gt 0 ] && [ $weeks_difference -le $retention_weeks ]; then
            if ! printf '%s\n' "${keep_for_weeks[@]}" | grep -Pq ^${weeks_difference}$; then
                echo "keeping (for weekly backup) $start_time $snapshot_id"
                keep_for_weeks+=($weeks_difference)
            else
                echo "deleting snapshot (less than $retention_weeks weeks) $start_time $snapshot_id"
                delete_snapshot $snapshot_id
            fi
        fi

    done
fi

