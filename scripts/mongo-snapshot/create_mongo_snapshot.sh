#!/bin/bash
set -o pipefail
source /opt/airflow/dags/scripts/common/utils.sh

volumeid=$1
snapshotName=$2
description=$3
environment=$4
mongo_backup_assume_role=$5

exit_on_empty $volumeid
exit_on_empty $snapshotName
exit_on_empty $description
exit_on_empty $environment

echo "creating snapshot ${snapshotName}"

if [ "x$mongo_backup_assume_role" != "xNone" ]; then
  echo "Assumung role"
  eval $(aws sts assume-role --role-arn "$mongo_backup_assume_role" --role-session-name RoleSessionNcinga | jq -r '.Credentials | "export AWS_ACCESS_KEY_ID=\(.AccessKeyId)\nexport AWS_SECRET_ACCESS_KEY=\(.SecretAccessKey)\nexport AWS_SESSION_TOKEN=\(.SessionToken)\n"')
fi

snapshot_id=$(aws ec2 create-snapshot --region ap-southeast-1 --volume-id $volumeid --description "$description" --tag-specifications "ResourceType=snapshot,Tags=[{Key=Name,Value=$snapshotName},{Key=airflow_pipeline,Value=mongo_backup},{Key=created_by_airflow,Value=true},{Key=Environment,Value=$environment}]" --output json | jq -r '.SnapshotId')

if [ $? != 0 ]; then
  echo "Failed to create snapshot on ${volumeid}"
  exit 1;
fi

#check for completion
count=3
exit_status=1
while [ $exit_status -eq 1 ] &&[ $count -ge 0 ]; do
  sleep 120
  snapshot_status=$(aws ec2 --region ap-southeast-1 describe-snapshots --snapshot-id "$snapshot_id" --output json | jq -r '.Snapshots[].State')
  echo "Snapshot Status : ${snapshot_status}"
  if [ "$snapshot_status" == "completed" ]; then
	echo "Snapshot ${snapshotName} completed"
    exit_status=0;
  fi
  count=$(( $count -  1 ))
done

exit $exit_status

