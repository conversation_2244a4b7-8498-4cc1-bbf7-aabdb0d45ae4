#!/bin/bash

source /opt/airflow/dags/scripts/common/utils.sh

ip=$1
port=$2
action=$3

username=`cat /home/<USER>/mongo-credentials/username`
password=`cat /home/<USER>/mongo-credentials/password`

exit_on_empty $ip
exit_on_empty $port
exit_on_empty $username
exit_on_empty $password
exit_on_empty $action


function get_current_status(){
  current_status=$(mongosh --quiet --host ${ip} --port ${port} --authenticationDatabase admin -u "${username}" admin -p "${password}" < /opt/airflow/dags/scripts/mongo-snapshot/check_mongo_locked.js | grep -oE 'false|true' | tr -d '\n')
}


function lock_mongo(){
  count=3;
  get_current_status
  while [ "$current_status" == "false" ] && [ $count -ge 0 ]; do
    #Lock the mongo
    mongosh --quiet --host ${ip} --port ${port} --authenticationDatabase admin -u ${username} admin -p ${password} --eval 'db.fsyncLock()'
    if [ $count != 3 ]; then
      echo "Retrying mongo lock - $count"
      sleep $(( 5 * $count ))
    fi
    count=$(( $count -  1 ))
    get_current_status
  done
  if [ "$current_status" != "true" ]; then
    echo "$(date) Failed to lock mongo on ${ip}:${port}"
    exit 1;
  fi
  echo "Sleeping 5 min to make sure all mongo writes are flushed"
  sleep 300;
}

function unlock_mongo(){
  count=3
  get_current_status
  while [ "$current_status" == "true" ] && [ $count -ge 0 ]; do
    #unLock the mongo
    mongosh --quiet --host ${ip} --port ${port} --authenticationDatabase admin -u ${username} admin -p ${password} --eval 'db.fsyncUnlock()'
    if [ $count != 3 ]; then
      echo "Retrying mongo unlock - $count"
      sleep $(( 5 * $count ))
    fi
    count=$(( $count -  1 ))
    get_current_status
  done
  if [ "$current_status" != "false" ]; then
    echo "Failed to unlock mongo $(date) on ${ip}:${port}"
    exit 1;
  fi
}


if [ "$action" == 'unlock-mongo' ]; then
  unlock_mongo
elif [ "$action" == 'lock-mongo' ]; then
  lock_mongo
fi

