#!/bin/bash
set -eu
output_file=$1
home="/home/<USER>"
stage_chef_config="$home/chef/client_stage.rb"
prod_chef_config="$home/chef/client_prod.rb"
#creating workspace
workspace="/opt/airflow/workspace"
mkdir -p $workspace

repo_url="*****************:16ab448e-0004-42a9-9070-e696c1/zilingo-chef.git"

#store cookbooks and its version in a file
/usr/bin/knife cookbook list -c ${stage_chef_config} > /tmp/stage_cookbook_list
/usr/bin/knife cookbook list -c ${prod_chef_config} > /tmp/prod_cookbook_list

#Compare version
vercomp() {
    # echo "inside vercomp with $1 and $2"
    if [[ $1 == $2 ]]
    then
        # echo "returning 0"
        echo "0"
        return 0
    fi
    local IFS=.
    local i ver1=($1) ver2=($2)
    # fill empty fields in ver1 with zeros
    for ((i=${#ver1[@]}; i<${#ver2[@]}; i++))
    do
        ver1[i]=0
    done
    for ((i=0; i<${#ver1[@]}; i++))
    do
        if [[ -z ${ver2[i]} ]]
        then
            # fill empty fields in ver2 with zeros
            ver2[i]=0
        fi
        if ((10#${ver1[i]} != 10#${ver2[i]}))
        then
            # echo "returning 1"
            echo "1"
            return 0
        fi
    done
    # echo "returning 0"
    echo "0"
    return 0
}

#update html
updateHtml() {
  if [[ $5 == 1 ]]; then
    echo '<html><body><table style="border: 1px solid black;border-collapse: collapse;">' > $output_file
    echo '<tr><th style="border: 1px solid black;border-collapse: collapse;" >Cookbook</th>' >> $output_file
    echo '<th style="border: 1px solid black;border-collapse: collapse;">master_repo</th>' >> $output_file
    echo '<th style="border: 1px solid black;border-collapse: collapse;">stage_chef</th>' >> $output_file
    echo '<th style="border: 1px solid black;border-collapse: collapse;">prod_chef</th></tr>' >> $output_file
  fi
  cat >> $output_file << EOF
  <tr><td style="border: 1px solid black;border-collapse: collapse; background: #ffffb3;">$1</td>
  <td style="border: 1px solid black;border-collapse: collapse;background: #ffffb3;">$2</td>
  <td style="border: 1px solid black;border-collapse: collapse;background: #ffffb3;">$3</td>
  <td style="border: 1px solid black;border-collapse: collapse;background: #ffffb3;">$4</td></tr>
EOF
}

clone_chef_repo() {
    cd $workspace
    folder=$(echo $repo_url | cut -d '/' -f2 | sed 's/.git//g')
    repo_dir="$workspace/$folder"
    echo "folder is $folder repo_dir is $repo_dir"

    if  [ -d $repo_dir ]; then
        cd $repo_dir
        git clean -f
        git fetch origin master
        git reset --hard  origin/master
        else
        git clone $repo_url
    fi
}

store_cookbooks_name() {
  local IFS=
  echo $(ls "${repo_dir}/cookbooks/") > /tmp/repo_cookbooks
  echo $(cat "/tmp/stage_cookbook_list" | awk '{print $1}') > /tmp/stage_cookbooks
  echo $(cat "/tmp/prod_cookbook_list" | awk '{print $1}') > /tmp/prod_cookbooks
}

clone_chef_repo
store_cookbooks_name

#Verify cookbook version
count=0
for cookbook in $(sort -u /tmp/repo_cookbooks /tmp/stage_cookbooks /tmp/prod_cookbooks); do
  if [[ "$cookbook" != "zilingo-reports" ]]; then
    cookbook_path="${repo_dir}/cookbooks/$cookbook"
    if [[ -f "${cookbook_path}/metadata.rb" ]]; then
      repo_cookbook_version=$(grep "^version " "${cookbook_path}/metadata.rb" | awk '{print $2}' | tr -d "'\"")
    elif [[ -f "${cookbook_path}/metadata.json" ]]; then
      repo_cookbook_version=$(cat "${cookbook_path}/metadata.json" | jq -r '.version')
    else
      repo_cookbook_version=""
    fi
    compare_repo_cookbook_version=${repo_cookbook_version}
    if [[ "x${repo_cookbook_version}" == "x" ]]; then
      repo_cookbook_version="<font color=\"black\"></font>"
      compare_repo_cookbook_version="0.0.0"
    fi

    stage_cookbook_version=$(cat /tmp/stage_cookbook_list | grep -i "^$cookbook " | awk '{print $2}')
    prod_cookbook_version=$(cat /tmp/prod_cookbook_list | grep -i "^$cookbook " | awk '{print $2}')

    compare_stage_cookbook_version=${stage_cookbook_version}
    compare_prod_cookbook_version=${prod_cookbook_version}

    stage_html="<font color=\"black\">"$stage_cookbook_version"</font>"
    prod_html="<font color=\"black\">"$prod_cookbook_version"</font>"

    if [[ "x${stage_cookbook_version}" == "x" ]]; then
      compare_stage_cookbook_version="0.0.0"
    fi
    if [[ "x${prod_cookbook_version}" == "x" ]]; then
      compare_prod_cookbook_version="0.0.0"
    fi

    compare_out=$(vercomp ${compare_repo_cookbook_version} ${compare_stage_cookbook_version})
    if [[ $compare_out -eq "1" ]]; then
      stage_html="<font color=\"red\">"$stage_cookbook_version"</font>"
      compare_out=$(vercomp ${compare_repo_cookbook_version} ${compare_prod_cookbook_version})
      if [[ $compare_out -eq "1" ]]; then
        prod_html="<font color=\"red\">"$prod_cookbook_version"</font>"
      fi
      ((++count))
      updateHtml "${cookbook}" "${repo_cookbook_version}" "${stage_html}" "${prod_html}" ${count}
    else
      compare_out=$(vercomp ${compare_repo_cookbook_version} ${compare_prod_cookbook_version})
      if [[ $compare_out -eq "1" ]]; then
        prod_html="<font color=\"red\">"$prod_cookbook_version"</font>"
        ((++count))
        updateHtml "${cookbook}" "${repo_cookbook_version}" "${stage_html}" "${prod_html}" ${count}
      fi
    fi
  fi
done

len=$(cat "$output_file" | wc -l)
if [[ $len > 0 ]]; then
  echo '</table></body></html>' >> $output_file
else
  echo '<html><body>There is no mismatch in cookbook version on master repo, stage-chef and prod-chef</body></html>' > $output_file
fi