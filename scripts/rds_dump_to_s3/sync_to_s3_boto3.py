# Using boto3 module, not being used as it doesn;t provide with parallel sync
import os
import boto3
import pprint
import sys
import botocore
import time
import logging

# Set up our logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger()

# Setup credentials
# os.environ["AWS_SHARED_CREDENTIALS_FILE"] = "~/.aws/snapshot2s3"
# os.environ["AWS_PROFILE"]="snapshot2s3"

rds_name = sys.argv[1]
db_name = sys.argv[2]
dump_timestamp = sys.argv[3]
path_to_dump_folder = sys.argv[4]
bucket_name = sys.argv[5]

region = 'ap-southeast-1'

s3_client = boto3.client('s3', region_name=region)

# We will parse each file and will sync
for root, dirs, files in os.walk(path_to_dump_folder):
    for file in files:
        s3_client.upload_file(os.path.join(
            root, file), bucket_name, f"{rds_name}/{db_name}/{dump_timestamp}/{file}")
