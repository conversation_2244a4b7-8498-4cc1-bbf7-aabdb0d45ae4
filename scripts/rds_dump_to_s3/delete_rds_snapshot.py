import os
import boto3
import sys
import botocore
import time
import logging

# Set up our logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger()

# Setup credentials
# os.environ["AWS_SHARED_CREDENTIALS_FILE"] = "~/.aws/snapshot2s3"
# os.environ["AWS_PROFILE"]="snapshot2s3"

db_snapshot_identifier = sys.argv[1]
region = sys.argv[2]

rds_client = boto3.client('rds',region_name=region)
retry_count = 0

while True:
    try:
        snapshot_deletion_info=rds_client.delete_db_snapshot(
            DBSnapshotIdentifier=db_snapshot_identifier
            )
        logger.info(f"snapshot {db_snapshot_identifier} is deleted")
        logger.info(snapshot_deletion_info)

    except rds_client.exceptions.DBSnapshotNotFoundFault as error:
        logger.info(f"snapshot {db_snapshot_identifier} doesn't exist! No need to delete.")

    except botocore.exceptions.ClientError as error:
        if error.response['Error']['Code'] == 'LimitExceededException' and retry_count < 5:
            retry_count = retry_count + 1
            logger.warn('API call limit exceeded; backing off and retrying...')
            time.sleep(10)
            continue
        else:
            raise error
    break