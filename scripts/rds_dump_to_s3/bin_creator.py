import sys
import logging
import json
import csv
import os
from airflow.models import Variable

# Set up our logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger()

# parse to get this annd loop over it
# _table_groupings_db_rds = sys.argv[1]

capacity = int(sys.argv[1])
accessible_table_list_folder = sys.argv[2]
rds = sys.argv[3]

# Function to distribute the tables (based on their sizes) into bins optimally
def firstFit(weights, count, capacity):
    current_bin_count = 0
    remaining_space = [0]*count
    final_distribution = [[] for i in range(count)]
    # Going element by element
    for i in range(count):
        # starting index for checking the bins
        j = 0
        minimum_space_bin_idx = 0
        minimum_space = capacity + 1
        # finding the optimal_bin where the space is minimum and the weight can be accomodated
        for j in range(current_bin_count):
            if(remaining_space[j] >= weights[i][0] and remaining_space[j] - weights[i][0] < minimum_space):
                minimum_space_bin_idx = j
                minimum_space = remaining_space[j] - weights[i][0]
        # It was not able to find any bin having space to accomodate the new weight
        if(minimum_space == capacity + 1):
            remaining_space[current_bin_count] = capacity - weights[i][0]
            final_distribution[current_bin_count].append(weights[i])
            current_bin_count += 1
        else:  # setting it in the optimal_bin
            remaining_space[minimum_space_bin_idx] -= weights[i][0]
            final_distribution[minimum_space_bin_idx].append(weights[i])
    table_groupings = []
    for _bin in final_distribution:
        if _bin != []:
            _table_group = {}
            for _table_obj in _bin:
                _table_group[_table_obj[1]] = _table_obj[0]
            table_groupings.append(_table_group)
    return table_groupings


for root, dirs, files in os.walk(accessible_table_list_folder):
    # The file names are db names
    for file in files:
        db = file
        absolute_path = os.path.join(root,file)
        with open(absolute_path) as _file:
            reader = csv.reader(_file)
            fetched_table_list = list(reader)
        
        tables = []
        sizes = []

        for fetched_table_obj in fetched_table_list:
            tables.append(fetched_table_obj[0])
            # Here sizes are in Bytes
            sizes.append(int(fetched_table_obj[1]))

        total_tables = len(tables)
        weights = list(zip(sizes, tables))
        table_groupings = firstFit(sorted(weights, reverse=True), total_tables, capacity)
        print(f"table_groupings_{db}_{rds} is {json.dumps(table_groupings)}")
        Variable.set('table_groupings_' + db + '_' + rds, json.dumps(table_groupings))