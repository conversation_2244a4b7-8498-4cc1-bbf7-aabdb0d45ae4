import botocore
import os
import boto3
import logging
import time
import sys

# Set up our logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger()

db_snapshot_identifier = sys.argv[1]
region = sys.argv[2]
db_subnet_group_name = sys.argv[3]
vpc_security_group_ids = []

for i in range(4, len(sys.argv)):
    vpc_security_group_ids.append(sys.argv[i])

rds_client = boto3.client('rds',region_name=region)
retry_count = 0

while True:
    try:
        db_instance_info = rds_client.describe_db_instances(
                DBInstanceIdentifier=db_snapshot_identifier
                )
        logger.info(f"db_instance {db_snapshot_identifier} already exists")
    except rds_client.exceptions.DBInstanceNotFoundFault as error:
        logger.info(f"db_instance {db_snapshot_identifier} doesn't exist. Creating...")
        # create db from snap
        retry_count_inner = 0
        while True:
            try:
                out=rds_client.restore_db_instance_from_db_snapshot(
                    DBInstanceIdentifier=db_snapshot_identifier,
                    DBSnapshotIdentifier=db_snapshot_identifier,
                    DBSubnetGroupName=db_subnet_group_name,
                    VpcSecurityGroupIds=vpc_security_group_ids,
                    Tags=[
                        {
                            'Key': 'created_by_airflow',
                            'Value': 'true'
                        },
                        {
                            'Key': 'airflow_pipeline',
                            'Value': 'snapshot2s3'
                        }
                    ]
                    )
                logger.info(out)
            except botocore.exceptions.ClientError as error:
                if error.response['Error']['Code'] == 'LimitExceededException' and retry_count_inner < 5:
                    retry_count_inner = retry_count_inner + 1
                    logger.warn('API call limit exceeded; backing off and retrying...')
                    time.sleep(10)
                    continue
                else:
                    raise error
            break

    except botocore.exceptions.ClientError as error:
        if error.response['Error']['Code'] == 'LimitExceededException' and retry_count < 5:
            retry_count = retry_count + 1
            logger.warn('API call limit exceeded; backing off and retrying...')
            time.sleep(10)
            continue
        else:
            raise error
    break

# Checking if the rds is available for queries or not
while(True):
    try:
        created_rds_info=rds_client.describe_db_instances(
                DBInstanceIdentifier=db_snapshot_identifier
                )
        rds_creation_status = created_rds_info['DBInstances'][0]['DBInstanceStatus']
        if(rds_creation_status == 'available'):
            logger.info(f"rds {db_snapshot_identifier} is available.")
            temp_rds_endpoint = created_rds_info['DBInstances'][0]['Endpoint']['Address']
            with open("/home/<USER>/temp_rds_endpoint","w") as _file:
                _file.write(temp_rds_endpoint)
            break
        else:
            logger.info(f"rds status is {rds_creation_status}. Sleeping for 30s and checking again...")
            time.sleep(30)
    except botocore.exceptions.ClientError as error:
        if error.response['Error']['Code'] == 'LimitExceededException' and retry_count < 5:
            retry_count = retry_count + 1
            logger.warn('API call limit exceeded; backing off and retrying...')
            time.sleep(10)
            continue
        else:
            raise error