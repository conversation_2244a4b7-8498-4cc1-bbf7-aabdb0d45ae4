import os
import boto3
import sys
import botocore
import time
import logging

# Set up our logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger()

db_instance_identifier = sys.argv[1]
db_instance_endpoint = sys.argv[2]
region = db_instance_endpoint.split(".")[2]
db_snapshot_identifier = sys.argv[3]

rds_client = boto3.client('rds', region_name=region)
retry_count = 0

# find if the snapshot is already available

while True:
    try:
        snapshot_info = rds_client.describe_db_snapshots(
            DBSnapshotIdentifier=db_snapshot_identifier,
            SnapshotType='manual'
        )
        logger.info(f"snapshot {db_snapshot_identifier} is already available")
        logger.info(snapshot_info)

    except rds_client.exceptions.DBSnapshotNotFoundFault as error:
        logger.info(
            f"snapshot {db_snapshot_identifier} doesn't exist. Creating...")
        retry_count_inner = 0
        # If snapshot is not available, create one
        while True:
            try:
                snapshot_creation_response = rds_client.create_db_snapshot(
                    DBSnapshotIdentifier=db_snapshot_identifier,
                    DBInstanceIdentifier=db_instance_identifier,
                    Tags=[
                        {
                            'Key': 'created_by_airflow',
                            'Value': 'true'
                        },
                        {
                            'Key': 'airflow_pipeline',
                            'Value': 'snapshot2s3'
                        }
                    ]
                )
                logger.info(
                    f"successfull snapshot creation triggered {db_snapshot_identifier}")
                logger.info(snapshot_creation_response)
            except botocore.exceptions.ClientError as error:
                if error.response['Error']['Code'] == 'LimitExceededException' and retry_count_inner < 5:
                    retry_count_inner = retry_count_inner + 1
                    logger.warn(
                        'API call limit exceeded; backing off and retrying...')
                    time.sleep(10)
                    continue
                else:
                    raise error
            break

    except botocore.exceptions.ClientError as error:
        if error.response['Error']['Code'] == 'LimitExceededException' and retry_count < 5:
            retry_count = retry_count + 1
            logger.warn('API call limit exceeded; backing off and retrying...')
            time.sleep(10)
            continue
        else:
            raise error
    break

# Checking the status of the snapshot
retry_count = 0
while(True):
    try:
        snapshot_creation_status = rds_client.describe_db_snapshots(
            DBSnapshotIdentifier=db_snapshot_identifier,
            SnapshotType='manual',
        )['DBSnapshots'][0]['Status']
        if(snapshot_creation_status == 'available'):
            logger.info("snapshot is available.")
            break
        else:
            logger.info(
                f"snapshot status is {snapshot_creation_status}. Sleeping for 30s and checking again...")
            time.sleep(30)
    except botocore.exceptions.ClientError as error:
        if error.response['Error']['Code'] == 'LimitExceededException' and retry_count < 5:
            retry_count = retry_count + 1
            logger.warn('API call limit exceeded; backing off and retrying...')
            time.sleep(10)
            continue
        else:
            raise error