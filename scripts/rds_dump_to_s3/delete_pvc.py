import json
from pprint import pformat
import sys
import subprocess
import logging
from airflow.models import Variable

rds_name = sys.argv[1]
db_name = sys.argv[2]

# Set up our logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger()

# Point to the internal API server hostname
APISERVER="https://kubernetes.default.svc"

# Path to ServiceAccount token
SERVICEACCOUNT="/var/run/secrets/kubernetes.io/serviceaccount"

# Read this Pod's namespace
with open(f"{SERVICEACCOUNT}/namespace", "r") as _file:
    NAMESPACE = _file.read()

# Read the ServiceAccount bearer token
with open(f"{SERVICEACCOUNT}/token", "r") as _file:
    TOKEN = _file.read()

# Reference the internal certificate authority (CA)
CACERT = f"{SERVICEACCOUNT}/ca.crt"

table_groupings = Variable.get('table_groupings_' + db_name + '_' + rds_name, deserialize_json=True)
for group_number in range(len(table_groupings)):
    pvc_name = f"{rds_name}-{db_name}-airflow-pvc-{group_number}".replace("_", "-").lower()
    cmd = """curl -s --cacert {0} --header "Authorization: Bearer {1}" -H 'Accept: application/json' -H 'Content-Type: application/json'  -X DELETE {2}/api/v1/namespaces/{3}/persistentvolumeclaims/{4}""".format(CACERT, TOKEN, APISERVER, NAMESPACE, pvc_name)
    process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    stdout, stderr = process.communicate()
    logger.info(f"process.returncode for deletion for pvc::{pvc_name} is {process.returncode}")
    logger.info(pformat(stdout))
    logger.error(pformat(stderr))
    if (process.returncode != 0):
        raise f"process.returncode for pvc::{pvc_name} is {process.returncode}"