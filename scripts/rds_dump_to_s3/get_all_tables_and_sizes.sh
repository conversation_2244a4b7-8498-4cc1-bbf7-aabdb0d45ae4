#!/bin/bash
set -eu


#This script geberates two file
# accessible_table_list_file_with_sizes: which will be used in the later steps of the pipeline
# non_accessible_table_list_file: Tables which are not accessible by the user, hence sending the mail with the file

endpoint=$1
rds_db_list_file=$2
accessible_table_list_folder=$3
non_accessible_table_list_folder=$4
non_accessible_tables_all_dbs=$5
username_file=$6
password_file=$7
#exporting password
password=`cat $password_file`
username=`cat $username_file`
export PGPASSWORD=$password

mkdir -p $accessible_table_list_folder
mkdir -p $non_accessible_table_list_folder
touch $non_accessible_tables_all_dbs


# This function gets the list of tables with their sizes for which user have access
get_tables_names_and_sizes() {
        db=$1
        accessible_table_list_file_with_sizes="$accessible_table_list_folder/$db"
        non_accessible_table_list_file="$non_accessible_table_list_folder/$db"
        all_tables_with_size=`pwd`/all_tables_with_size
        all_tables_with_access=`pwd`/all_tables_with_access
        all_tables=`pwd`/all_tables

        # Getting the list of all tables in the database and their sizes in Bytes sorted by table name
        psql -A -F "," --dbname=$db --host=$endpoint -U $username -o $all_tables_with_size -t -c "SELECT tablename, pg_total_relation_size(quote_ident(tablename)) FROM pg_catalog.pg_tables WHERE schemaname != 'pg_catalog' AND schemaname != 'information_schema' order by tablename"

        # Getting a  list of all the tables for which dblink_read user has access sorted by table_name
        psql -A -F "," --dbname=$db --host=$endpoint -U $username -o $all_tables_with_access -t -c "SELECT DISTINCT table_name FROM information_schema.role_table_grants order by table_name"

	#sorting all_tables_with_access
	sort -o $all_tables_with_access $all_tables_with_access

        # Storing all_tables in a separate file without the sizes
        cat $all_tables_with_size | sed 's/,.*$//g'| sort > $all_tables

        #find the tables for which user doesn't have access
        comm -23 $all_tables $all_tables_with_access > $non_accessible_table_list_file

        #create the regex for grep -vE "table1,|table2,"
        reg=""
        for table_without_access in `cat $non_accessible_table_list_file`; do
                reg="^$table_without_access,|$reg"
        done

        # Removing the trailing |
        final_regex=`echo $reg | sed 's/|$//g'`
	if [ "x$final_regex" == "x" ]; then
                cp $all_tables_with_size  $accessible_table_list_file_with_sizes
        else
                cat $all_tables_with_size | grep -vE "$final_regex" || true > $accessible_table_list_file_with_sizes
        fi
}
echo "content of $rds_db_list_file"
cat $rds_db_list_file
cat $rds_db_list_file | jq -r '.available[]'
for db in `cat $rds_db_list_file | jq -r '.available[]'`; do
        echo "calling get_tables_names_and_sizes $db"
        get_tables_names_and_sizes $db
done

# We will have all the tables and sizes for different dbs inside $accessible_table_list_folder
# We will have the tables for which we don't have access inside $non_accessible_table_list_folder


# Creating a single file for non accessible tables which can be sent as mail on rds level
for x in `ls $non_accessible_table_list_folder`; do
	db_name=`basename $x`
	if [ -s "$non_accessible_table_list_folder/$x" ]; then
                echo "for db:$db_name, following tables didn't have the correct access:" >> $non_accessible_tables_all_dbs
                cat "$non_accessible_table_list_folder/$x" >> $non_accessible_tables_all_dbs
                echo "" >> $non_accessible_tables_all_dbs
	fi
done