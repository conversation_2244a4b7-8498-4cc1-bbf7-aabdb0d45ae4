import json
from pprint import pformat
import sys
import subprocess
import logging
from math import ceil
from airflow.models import Variable

print(sys.argv)
rds_name = sys.argv[1]
db_name = sys.argv[2]

table_grouping = Variable.get('table_groupings_' + db_name + '_' + rds_name,deserialize_json=True)

# Set up our logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger()

# Point to the internal API server hostname
APISERVER="https://kubernetes.default.svc"

# Path to ServiceAccount token
SERVICEACCOUNT="/var/run/secrets/kubernetes.io/serviceaccount"

# Read this Pod's namespace
with open(f"{SERVICEACCOUNT}/namespace", "r") as _file:
    NAMESPACE = _file.read()

# Read the ServiceAccount bearer token
with open(f"{SERVICEACCOUNT}/token", "r") as _file:
    TOKEN = _file.read()

# Reference the internal certificate authority (CA)
CACERT = f"{SERVICEACCOUNT}/ca.crt"

def get_pvc_json(claim_name, claim_size_in_kilo):
    default_template_json = """
    {
        "kind": "PersistentVolumeClaim",
        "apiVersion": "v1",
        "metadata": {
        "name": "claim_name"
        },
        "spec": {
            "accessModes": [
            "ReadWriteOnce"
            ],
            "resources": {
                "requests": {
                "storage": "claim_size"
                }
            },
            "storageClassName": "ebs-sc"
        }
    }"""
    template_dictionary = json.loads(default_template_json)
    template_dictionary['metadata']['name'] = claim_name
    template_dictionary['spec']['resources']['requests']['storage'] = f"{claim_size_in_kilo}Ki"
    return json.dumps(template_dictionary)



group_number = 0
for group_info in table_grouping:
    logger.info("+++++++++++++++++++++++++++++++++++++++++++++")
    group_size = ceil(sum(group_info.values())*1.25/1024) #Converted to 1.25 times KiBytes (1024 Bytes)
    pvc_name = f"{rds_name}-{db_name}-airflow-pvc-{group_number}".replace("_", "-").lower()
    tables_in_group = ', '.join(group_info.keys())
    print(f"pvc_name: {pvc_name} \n group_size: {group_size}KiB \n tables: {tables_in_group} ")
    pvc_json = get_pvc_json(pvc_name, group_size)
    cmd = """curl -s --cacert {0} --header "Authorization: Bearer {1}" -H 'Accept: application/json' -H 'Content-Type: application/json'  -X POST {2}/api/v1/namespaces/{3}/persistentvolumeclaims -d '{4}'""".format(CACERT, TOKEN, APISERVER, NAMESPACE, pvc_json)
    group_number +=1
    process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    stdout, stderr = process.communicate()
    logger.info(f"process.returncode for pvc::{pvc_name} is {process.returncode}")
    logger.info("stdout=====> \n {}".format(stdout).replace('\\n', '\n').replace('\\t', '\t'))
    logger.info("stderr=====> {}".format(stderr).replace('\\n', '\n').replace('\\t', '\t'))
    if (process.returncode != 0):
        raise f"process.returncode for pvc::{pvc_name} is {process.returncode}"
    logger.info("+++++++++++++++++++++++++++++++++++++++++++++")