# This script gets all the rds with status available.
# Currently for testing, we are using hardocded_rds for testing on a single rds
# fetches and stores the required params in variable fetched_rds_list  

import botocore
import os
import boto3
import pprint
import time
import json
import logging
from airflow.models import Variable

# Set up our logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger()


def is_created_by_airflow(tags_list):
    for tag_obj in tags_list:
        if(tag_obj['Key'] == 'created_by_airflow' and tag_obj['Value'] == 'true'):
            return True
    return False

fetched_rds_list = {}
regions = ['ap-southeast-1', 'ap-south-1']
for region in regions:
    rds_client = boto3.client('rds', region_name=region)
    retry_count = 0
    while True:
        try:
            all_rds = rds_client.describe_db_instances()['DBInstances']
            for rds_info in all_rds:
                if((rds_info['DBInstanceStatus'] == 'available') and (is_created_by_airflow(rds_info['TagList']) == False)):
                    temp = {}
                    name = rds_info['DBInstanceIdentifier']
                    endpoint = rds_info['Endpoint']['Address']
                    dB_subnet_group_name = rds_info['DBSubnetGroup']['DBSubnetGroupName']
                    vpc_security_group_ids = [vpc_security_group['VpcSecurityGroupId']
                                              for vpc_security_group in rds_info['VpcSecurityGroups']]
                    temp['endpoint'] = endpoint
                    temp['subnet_group_name'] = dB_subnet_group_name
                    temp['vpc_security_group_ids'] = vpc_security_group_ids
                    fetched_rds_list[name] = temp
        except botocore.exceptions.ClientError as error:
            if error.response['Error']['Code'] == 'LimitExceededException' and retry_count < 5:
                retry_count = retry_count + 1
                logger.warn(
                    'API call limit exceeded; backing off and retrying...')
                time.sleep(10)
                continue
            else:
                raise error
        break

# Setting the fetched_rds_list variable in Airflow
Variable.set('fetched_rds_list', json.dumps(fetched_rds_list))