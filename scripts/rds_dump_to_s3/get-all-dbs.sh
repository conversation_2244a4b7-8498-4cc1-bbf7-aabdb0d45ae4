#!/bin/bash
set -eu

endpoint=$1
username_file=$2
password_file=$3
output_file=$4

password=`cat $password_file`
username=`cat $username_file`
export PGPASSWORD=$password

# Getting the list of all databases
psql -A -R " " --dbname=postgres --host=$endpoint -U $username -o $output_file -t -c "SELECT pg_database.datname FROM pg_database WHERE pg_database.datname NOT IN ('rdsadmin', 'postgres') AND pg_database.datname NOT LIKE 'template%'"