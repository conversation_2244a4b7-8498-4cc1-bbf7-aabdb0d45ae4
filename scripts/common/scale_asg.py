# custom desired capacity can be passed.
# Default capacity will be taken from variable 'nodegroup_info'

import botocore
import boto3
import time
import logging
import sys
from airflow.models import Variable

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger()

nodegroup_info = Variable.get('nodegroup_info',deserialize_json=True)
asg_name = nodegroup_info['asg_name']
try:
    desired_capacity = int(sys.argv[1])
except IndexError:
    desired_capacity = nodegroup_info['desired_capacity']
region = 'ap-southeast-1'

autoscaling_client = boto3.client('autoscaling', region_name=region)
retry_count=0
while True:
        try:
            autoscaling_client.set_desired_capacity(
                AutoScalingGroupName=asg_name,
                DesiredCapacity=desired_capacity
            )
        except botocore.exceptions.ClientError as error:
            if error.response['Error']['Code'] == 'LimitExceededException' and retry_count < 5:
                retry_count = retry_count + 1
                logger.warn(
                    'API call limit exceeded; backing off and retrying...')
                time.sleep(10)
                continue
            else:
                raise error
        break