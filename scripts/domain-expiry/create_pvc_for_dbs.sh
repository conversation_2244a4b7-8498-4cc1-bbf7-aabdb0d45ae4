# Point to the internal API server hostname
APISERVER=https://kubernetes.default.svc

# Path to ServiceAccount token
SERVICEACCOUNT=/var/run/secrets/kubernetes.io/serviceaccount

# Read this Pod's namespace
NAMESPACE=$(cat ${SERVICEACCOUNT}/namespace)

# Read the ServiceAccount bearer token
TOKEN=$(cat ${SERVICEACCOUNT}/token)

# Reference the internal certificate authority (CA)
CACERT=${SERVICEACCOUNT}/ca.crt

pvc_json=$(cat /k8s/pvc.json)

rds_name=$1
all_dbs=$2
for db_name in $all_dbs; do
    definition=$(cat <<EOF
{
   "kind": "PersistentVolumeClaim",
   "apiVersion": "v1",
   "metadata": {
      "name": "pvc_airflow_${rds_name}_${db_name}"
   },
   "spec": {
      "accessModes": [
         "ReadWriteOnce"
      ],
      "resources": {
         "requests": {
            "storage": "100Mi"
         }
      },
      "storageClassName": "airflow-worker"
   }
}
EOF
)
echo $definition | jq '.'

done


# Explore the API with TOKEN
curl --cacert ${CACERT} --header "Authorization: Bearer ${TOKEN}" -H 'Accept: application/json' -H 'Content-Type: application/json'  -X POST ${APISERVER}/api/v1/namespaces/${NAMESPACE}/persistentvolumeclaims -d "$pvc_json"
create_volume.sh (END)