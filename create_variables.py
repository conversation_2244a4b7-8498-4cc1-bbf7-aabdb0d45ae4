from datetime import timedelta
from datetime import datetime
from os import listdir
from posixpath import basename
from airflow import DAG
from airflow.operators.bash import BashOperator
from airflow.operators.python import PythonOperator
from airflow.operators.python import BranchPythonOperator
from airflow.utils.dates import days_ago
from airflow.models import Variable
from airflow.utils.email import send_email
from pathlib import Path
from kubernetes.client import models as k8s
import logging
from os.path import isfile, join

log = logging.getLogger(__name__)

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email': ['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(seconds=30),
}

PARENT_DAG_NAME='create_airflow_variables'

dag = DAG(
    PARENT_DAG_NAME,
    default_args=default_args,
    description='DAG to create varibales/connections from secrets and config maps',
    schedule_interval=None,
    max_active_runs=1,
    start_date=days_ago(2),
    tags=['Devops']
)

def _get_files(dir_path):
    return [join(dir_path,f) for f in listdir(dir_path) if isfile(join(dir_path, f))]

def _create_variables_from_files(file_list):
    for file_path in file_list:
        file_name=basename(file_path)
        with open(file_path) as _file:
            value = _file.read()
        Variable.set(file_name,value)

def _create_variables(**kwargs):
    _create_variables_from_files(_get_files("/opt/airflow/secrets"))
    _create_variables_from_files(_get_files("/opt/airflow/config-maps"))

create_airflow_volumes = PythonOperator(
    task_id = 'create_variables_from_mounted_secrets',
    dag = dag,
    python_callable = _create_variables,
    executor_config={
        "pod_override": k8s.V1Pod(
            spec=k8s.V1PodSpec(
                security_context=k8s.V1PodSecurityContext(
                    fs_group=50000,run_as_group=50000,run_as_user=50000
                ),
                containers=[
                        k8s.V1Container(
                            name="base",
                            volume_mounts=[
                                k8s.V1VolumeMount(
                                    mount_path="/opt/airflow/secrets", name="secret-mount"
                                ),
                                k8s.V1VolumeMount(
                                    mount_path="/opt/airflow/config-maps", name="configmap-mount"
                                )
                            ],
                        )
                    ],
                volumes=[
                        k8s.V1Volume(
                            name="secret-mount",
                            secret=k8s.V1SecretVolumeSource(secret_name="airflow-variables-secret"),
                        ),
                        k8s.V1Volume(
                            name="configmap-mount",
                            config_map=k8s.V1ConfigMapVolumeSource(name="airflow-variables-configmap"),
                        ),

                    ],
            )
        )
    }
)

create_airflow_volumes
