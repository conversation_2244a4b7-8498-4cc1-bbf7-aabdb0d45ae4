#Dag for taking EBS disks backups
from airflow.operators.python import <PERSON><PERSON><PERSON><PERSON><PERSON>perator, PythonOperator
from airflow.operators.dummy import DummyOperator
from airflow.utils.email import send_email
from airflow.utils.dates import days_ago
from airflow.models import Variable
from airflow import DAG
from kubernetes.client import models as k8s
from datetime import datetime, timedelta
import logging
import boto3
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
from utility.bashUtils import get_subscribers
from utility.airflowUtils import get_base_executor_config


log = logging.getLogger(__name__)

PARENT_DAG_NAME = "BACKUP_EBS"
aws_region = 'ap-southeast-1'

email_group = get_subscribers(PARENT_DAG_NAME)

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email': email_group,
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(seconds=60)
}

dag = DAG(
    PARENT_DAG_NAME,
    default_args=default_args,
    description='Dag to take backup of EBS disks',
    schedule_interval='0 20 * * *', #every 1.30 am IST
    catchup=False,
    max_active_runs=1,
    start_date=days_ago(2),
    tags=['Devops']
)


def _select_ebs_disks(**kwargs):
    selected_tasks = []
    for ebs in ebs_backup_list['ebs']:
        if 'skip' in ebs and ebs['skip'] == "true": #skip when skip = true is set
            continue
        node_name = ebs['node_name']
        selected_tasks.append(node_name)
    return selected_tasks


def _create_snapshot(**kwargs):
    node_name =  kwargs['node_name']
    volume_id = kwargs['volume_id']
    environment = kwargs['environment']
    ec2_client = _get_ec2_client(environment)
    pending_snapshots = []
    pending_hours = 6
    #check if there are pending snapshots and send mail if present
    filters = [
        {'Name': 'volume-id', 'Values': [volume_id]},
        {'Name': 'tag:created_by_airflow', 'Values': ['true']},
        {'Name': 'tag:airflow_pipeline', 'Values': ["ebs_backup"]},
        {'Name': 'status', 'Values': ['pending']}
    ]
    snapshot_response = ec2_client.describe_snapshots(Filters=filters)
    for snapshot in snapshot_response['Snapshots']:
        snapshot_time = snapshot['StartTime']
        if ((datetime.utcnow().date() - snapshot_time.date()).seconds)/(60*60) > pending_hours:
            pending_snapshots.append(snapshot['SnapshotId'])
    if pending_snapshots:
        mail_body = "Following snapshots of {} are in pending state from more than {} hours:\n{}". \
                    format(volume_id, pending_hours, "\n".join(pending_snapshots))
        title = "Airflow: Pending Snapshots"
        send_email(email_group, title, mail_body)
    #create snapshot
    snapshot_name = node_name + "-backup-"  + datetime.utcnow().strftime('%Y-%m-%d-%H')
    try:
        snapshot = ec2_client.create_snapshot(
            VolumeId=volume_id,
            Description="snapshot of volume {}, EBS disk of node {}".format(volume_id, node_name),
            TagSpecifications=[
            {
                'ResourceType': 'snapshot',
                'Tags' : [
                    {'Key': 'Name', 'Value': snapshot_name},
                    {'Key': 'created_by_airflow', 'Value': 'true'},
                    {'Key': 'airflow_pipeline', 'Value': "ebs_backup"}
                ]
            }],
        )
        snapshot_id = snapshot['SnapshotId']
        log.info("Creating snapshot {}".format(snapshot_id))
    except:
        raise Exception("Failure in snapshot creation")


def _delete_old_snapshots(**kwargs):
    volume_id = kwargs['volume_id']
    retention_days = kwargs['retention_days']
    environment = kwargs['environment']
    ec2_client = _get_ec2_client(environment)
    filters = [
        {'Name': 'volume-id', 'Values': [volume_id]},
        {'Name': 'tag:created_by_airflow', 'Values': ['true']},
        {'Name': 'tag:airflow_pipeline', 'Values': ["ebs_backup"]}
    ]
    snapshot_response = ec2_client.describe_snapshots(Filters=filters)
    for snapshot in snapshot_response['Snapshots']:
        snapshot_time = snapshot['StartTime']
        if (datetime.utcnow().date() - snapshot_time.date()).days >= retention_days:
            snapshot_id = snapshot['SnapshotId']
            log.info("Deleting snapshot {}".format(snapshot_id))
            try:
                ec2_client.delete_snapshot(SnapshotId=snapshot_id)
            except:
                raise Exception("Failure in snapshot deletion")


def _get_ec2_client(environment):
    if environment in backup_assume_roles.keys():
        backup_assume_role = backup_assume_roles[environment]
        boto_sts = boto3.client('sts')
        sts_response = boto_sts.assume_role(
            RoleArn=backup_assume_role,
            RoleSessionName='newsession'
        )
        newsession_id = sts_response["Credentials"]["AccessKeyId"]
        newsession_key = sts_response["Credentials"]["SecretAccessKey"]
        newsession_token = sts_response["Credentials"]["SessionToken"]
        ec2_client = boto3.client('ec2',
            region_name = aws_region,
            aws_access_key_id=newsession_id,
            aws_secret_access_key=newsession_key,
            aws_session_token=newsession_token)
    else:
        ec2_client = boto3.client('ec2', region_name=aws_region)
    return ec2_client


select_ebs_disks = BranchPythonOperator(
    task_id='select_ebs_disks',
    dag=dag,
    python_callable=_select_ebs_disks
)


ebs_backup_list = Variable.get('ebs_backup_list', deserialize_json=True)
service_account = ebs_backup_list['service_account']
backup_assume_roles = ebs_backup_list['backup_assume_roles']
executor_config = get_base_executor_config(service_account)

for ebs in ebs_backup_list['ebs']:
    node_name = ebs['node_name']
    volume_id = ebs['volume_id']
    retention_days = ebs['retention_days']
    environment = ebs['environment']

    start = DummyOperator(task_id=node_name, dag=dag)

    create_snapshot = PythonOperator(
        task_id='create_snapshot_{}'.format(node_name),
        dag=dag,
        python_callable=_create_snapshot,
        op_kwargs={'node_name': node_name, 'volume_id': volume_id, 'environment': environment},
        executor_config=executor_config
    )

    delete_old_snapshots = PythonOperator(
        task_id='delete_old_snapshots_{}'.format(node_name),
        dag=dag,
        python_callable=_delete_old_snapshots,
        op_kwargs={'volume_id': volume_id, 'retention_days': retention_days, 'environment': environment},
        executor_config=executor_config
    )

    select_ebs_disks.set_downstream(start)
    start.set_downstream(create_snapshot)
    start.set_downstream(delete_old_snapshots)


select_ebs_disks

