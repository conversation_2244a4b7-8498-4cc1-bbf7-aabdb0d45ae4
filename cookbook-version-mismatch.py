from datetime import timedelta
from textwrap import dedent
from airflow import DAG

from airflow.operators.bash import BashOperator
from airflow.operators.python import PythonOperator
from pprint import pprint
from airflow.operators.dummy import DummyOperator
from airflow.utils.dates import days_ago
from airflow.models import Variable
from airflow.utils.email import send_email
from pathlib import Path
import logging
import socket
import time
import os
import sys
import subprocess
from kubernetes.client import models as k8s
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
from utility.bashUtils import execute_command, read_file, write_to_file, get_subscribers

log = logging.getLogger(__name__)

PARENT_DAG_NAME = 'Cookbook-Version-Mismatch'

email_group = get_subscribers(PARENT_DAG_NAME)

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email': email_group,
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(seconds=30),
}

dag = DAG(
    PARENT_DAG_NAME,
    default_args=default_args,
    description='Finds the version mimatch in chef cookbooks on production, stage and bitbucket repo',
    schedule_interval='30 7 * * 1', # Every Monday at 7:30UTC
    catchup = False,
    max_active_runs = 1,
    start_date=days_ago(40), #Setting this, so that there always a DAG run when the dag is switched on
)

# This runs cookbook_version_mismatch/main.sh which inturn clones the zilingo-chef and compare the versions on stage/prod chef using knife
def _get_cookbook_version_mismatch(**kwargs):
    output_file = "/tmp/version_table.html"
    execute_command(["bash", "/opt/airflow/dags/scripts/cookbook_version_mismatch/main.sh",output_file], shell=False)
    mail_body = read_file(output_file)
    title = "Airflow: cookbooks_version_mismatch"
    send_email(email_group, title, mail_body)


# Binding chef-credentials and ci-git-credentials to the task as volumes.
cookbook_version_mismatch_task = PythonOperator(
    task_id='cookbook-version-mismatch',
    python_callable=_get_cookbook_version_mismatch,
    dag=dag,
    executor_config={
        "pod_override": k8s.V1Pod(
            spec=k8s.V1PodSpec(
                security_context=k8s.V1PodSecurityContext(
                    fs_group=50000,run_as_group=50000,run_as_user=50000
                ),
                containers=[
                        k8s.V1Container(
                            name="base",
                            volume_mounts=[
                                k8s.V1VolumeMount(
                                    mount_path="/home/<USER>/chef", name="chef-secret"
                                ),
                                k8s.V1VolumeMount(
                                    mount_path="/home/<USER>/.ssh", name="git-secret"
                                )
                            ],
                        )
                    ],
                volumes=[
                        k8s.V1Volume(
                            name="chef-secret",
                            secret=k8s.V1SecretVolumeSource(secret_name="chef-credentials"),
                        ),
                        k8s.V1Volume(
                            name="git-secret",
                            secret=k8s.V1SecretVolumeSource(secret_name="ci-git-credentials"),
                        ),

                    ],
            )
        )
    }
)

cookbook_version_mismatch_task
