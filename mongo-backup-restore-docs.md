# Document: Understanding the `mongo_backup_restore_s3` DAG

This document breaks down the Airflow DAG responsible for backing up and restoring MongoDB clusters.

---

### 1. High-Level Goal

The primary purpose of this DAG is to automate the process of creating full daily backups for multiple MongoDB clusters, securely uploading them to an AWS S3 bucket, and performing a periodic data integrity check.

The integrity check involves:
1.  Taking a snapshot of document counts *before* the process begins.
2.  Restoring the latest backup to a temporary, isolated MongoDB instance.
3.  Counting the documents in the restored database.
4.  Comparing the "before" and "after" counts to ensure the backup is valid and complete.

This entire process is containerized and runs on Kubernetes, ensuring a clean, isolated, and reproducible environment for each task.

---

### 2. Prerequisites

Before running this DAG, ensure the following are configured:

*   **Kubernetes Cluster:** An active Kubernetes cluster with a configured `StorageClass` named `ebs-sc-delete` that can dynamically provision Persistent Volumes.
*   **Airflow Connection:** An Airflow connection named `solvei8-stateful-backups-connection` of type `Amazon Web Services` with credentials that have read/write access to the target S3 bucket.
*   **Airflow Variable:** An Airflow Variable named `mongo_backup_restore_s3` must exist and be populated with the correct JSON structure (detailed in section 6).
*   **<PERSON><PERSON>netes Service Account:** The `airflow` service account in the `airflow` namespace needs permissions to create, list, and delete `PersistentVolumeClaims`.

---

### 3. Visual Workflow

```mermaid
graph TD
    subgraph For Each Cluster
        A[Backup with mongodump] --> B[Upload to S3];
        B --> C{It's Wednesday?};
        C -->|Yes| D[Check Original Document Counts];
        C -->|No| E[Skip Restore];
        D --> F[Restore to Temp Pod];
        F --> G[Verify Restored Counts];
    end

    Start[Start] --> CreatePVCs[Create All PVCs];
    CreatePVCs --> A;
    G --> Cleanup[Cleanup All PVCs];
    E --> Cleanup;
    Cleanup --> End[End];
```

---

### 4. How It Works: Execution Flow

The DAG runs on a schedule (`30 21 * * *`, which is 9:30 PM UTC daily). Here is the step-by-step workflow:

1.  **Start & Prepare Storage (`create_all_pvcs`):** The DAG begins by running a pod to calculate the required storage size for each MongoDB cluster. It then dynamically creates a Persistent Volume Claim (PVC) for each cluster. On Wednesdays, it also creates a second PVC for the test restore.

2.  **Process Clusters in Parallel:** The DAG uses `TaskGroups` to process each cluster in parallel. For each cluster:
    *   **A. Backup (`run_mongodump_*`):** A pod performs a `mongodump`, storing the backup in its PVC.
    *   **B. Upload (`upload_to_s3_*`):** A Python task uploads the backup from the PVC to S3.
    *   **C. Decide to Restore (`branch_on_day_*`):** A branch operator checks the day.
        *   **Wednesday:** Proceeds to the restore and verification steps.
        *   **Other Days:** Skips the restore.

3.  **Restore & Verify (Wednesday Only):**
    *   **A. Pre-Restore Check (`check_collections_*`):** A pod connects to the *original* MongoDB cluster and counts all documents, saving the result.
    *   **B. Restore (`restore_mongodb_*`):** A pod starts a temporary MongoDB instance and uses `mongorestore` to load the backup into it.
    *   **C. Post-Restore Verification (`verify_restore_counts_*`):** A Python task compares the pre-restore and post-restore document counts and logs a detailed report.

4.  **Cleanup (`cleanup_pvcs`):** A final pod deletes all PVCs created during the run. This task runs regardless of whether the previous steps succeeded or failed (`trigger_rule='all_done'`).

---

### 5. Operator Explanations

*   `KubernetesPodOperator`: Runs command-line tools (`mongodump`, `kubectl`) in isolated, containerized environments with specific resource allocations and storage.
*   `PythonOperator`: Executes Python code efficiently for tasks like interacting with AWS S3 and comparing XCom data.
*   `BranchPythonOperator`: Creates conditional workflows, enabling the "run restore only on Wednesday" logic.
*   `DummyOperator`: Acts as a clean endpoint for a conditional branch, improving DAG readability.
*   `TaskGroup`: Organizes the UI by grouping all tasks related to a single cluster, making monitoring easier.

---

### 6. Variables Explained

The DAG is driven by an Airflow Variable named `mongo_backup_restore_s3`.

**Structure:**
A JSON object with a `"clusters"` key, which is a list of cluster objects.

**Example:**
```json
{
  "clusters": [
    {
      "name": "stage-mongo-3-3",
      "mongo_host": "*************",
      "mongo_port": "27013",
      "s3_bucket_name": "solvei8-stateful-backups",
      "s3_backup_folder": "mongo/stage/full_backups_mongo-3-3",
      "mongo_username": "oploguser",
      "mongo_password": "6489aXGg6QZi",
      "auth_database": "admin"
    }
  ]
}
```

**Key Usage:**
*   `name`: Uniquely identifies tasks, groups, and PVCs.
*   `mongo_*`: Connection details for `mongodump` and `mongorestore`.
*   `s3_*`: Target location for the backup upload.

---

### 7. Security & Best Practices

*   **Credentials:** MongoDB and AWS credentials are not hardcoded. They are pulled from the Airflow Variable and Connection store at runtime, which is more secure.
*   **Isolation:** Using the `KubernetesPodOperator` ensures that backup and restore operations for different clusters do not interfere with each other or the main Airflow components.
*   **Cleanup:** The `cleanup_pvcs` task with `trigger_rule='all_done'` is crucial for preventing orphaned storage volumes, which can lead to unnecessary costs.

---

### 8. Debugging

*   **Check Pod Logs:** If a task fails, the first step is to check the logs of the corresponding Kubernetes pod. You can find the pod name in the Airflow task log. Use `kubectl logs <pod-name> -n airflow` to retrieve them.
*   **Inspect PVCs:** If a task is stuck in a "pending" state, it might be due to a PVC issue. Use `kubectl get pvc -n airflow` to check the status of the claims. Use `kubectl describe pvc <pvc-name> -n airflow` to see why it might not be binding.
*   **XComs:** The data passed between tasks (like document counts and backup paths) is stored in XComs. You can view these in the Airflow UI to trace the data flow and verify that each step is producing the correct output.