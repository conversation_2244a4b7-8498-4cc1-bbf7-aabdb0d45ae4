######################################################################
######################################################################
########  Main DAG File for taking & pushing RDS-dumps to s3  ########
######################################################################
######################################################################

from datetime import timedelta
from datetime import datetime
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.python import BranchPythonOperator
from airflow.api.common.experimental.pool import get_pool, create_pool
from airflow.exceptions import PoolNotFound
from airflow.utils.dates import days_ago
from airflow.models import Variable
from airflow.utils.email import send_email
from pathlib import Path
from kubernetes.client import models as k8s
import json
import csv
import logging
import socket
import time
import math
import os
import sys
import subprocess
from os.path import expanduser
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
from utility.bashUtils import execute_command, read_file, write_to_file, get_week_number, get_subscribers

log = logging.getLogger(__name__)

PARENT_DAG_NAME = 'BACKUP_POSTRES'


email_group = get_subscribers(PARENT_DAG_NAME)

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email': email_group,
    'email_on_failure': True,
    'email_on_retry': False,
    'retries':1 ,
    'retry_delay': timedelta(seconds=30),
}

dag = DAG(
    PARENT_DAG_NAME,
    default_args=default_args,
    description='DAG to take dumps of each RDS and push them to s3',
    schedule_interval='30 2 * * *', #“At 02:30 everyday”
    max_active_runs=1,
    catchup = False,
    start_date=days_ago(10),
    # tags=['twice-a-week']
    tags= ['daily', 'prod', 'backup']
)

######################################################################

##### Functions used for all the tasks declared in this DAG #####

# Function to fetch the list of all RDS and store it in variable rds_list
def _get_all_rds_list(**kwargs):
    rds_list = Variable.get('rds_list', deserialize_json=True)
    for rds in rds_list:
        rds_list[rds]['is_live'] = False
    log.info("Executing Python Script for getting list of RDS..")
    execute_command(
        ["python3", "/opt/airflow/dags/scripts/rds_dump_to_s3/get_all_rds.py"], shell=False)
    fetched_rds_list = Variable.get('fetched_rds_list', deserialize_json=True)
    for rds in fetched_rds_list:
        if rds in rds_list:
            rds_list[rds]['endpoint'] = fetched_rds_list[rds]['endpoint']
            rds_list[rds]['subnet_group_name'] = fetched_rds_list[rds]['subnet_group_name']
            rds_list[rds]['vpc_security_group_ids'] = fetched_rds_list[rds]['vpc_security_group_ids']
            rds_list[rds]['is_live'] = True
        else:
            fetched_rds_json = {"endpoint": fetched_rds_list[rds]['endpoint'], "subnet_group_name": fetched_rds_list[rds]['subnet_group_name'], "vpc_security_group_ids": fetched_rds_list[rds]['vpc_security_group_ids'], "is_live": True}
            rds_list[rds] = fetched_rds_json
    Variable.set('rds_list', json.dumps(rds_list))
    return get_week_number(datetime.utcnow())


# Function to select the RDS which are live and their dumps needed to be taken
# Returns task_id_list which can be accessed using xcom

def _select_required_rds(**kwargs):
    task_id_list = ['Leveller']
    execution_week_number = int(kwargs['ti'].xcom_pull(task_ids='Get_All_RDS_List'))
    rds_list = Variable.get('rds_list', deserialize_json=True)
    force_run = False
    # If hardcoded list exists, use that only, else use all
    try:
        hardcoded_rds = Variable.get('hardcoded_rds', deserialize_json = True)
        if len(hardcoded_rds) > 0:
            force_run = True
            for k in list(rds_list.keys()):
                if k not in hardcoded_rds:
                    rds_list.pop(k, None)
    except:
        log.info(f"No hardcoded list found using all the rds")
    
    # If we want to skip some rds, that can bee provided in the variable skip_rds as ["skip_rds1", "skip_rds2"]
    try:
        rds_to_be_skipped = Variable.get('skip_rds', deserialize_json = True)
        for k in list(rds_list.keys()):
            if k in rds_to_be_skipped:
                rds_list.pop(k, None)
    except:
        log.info(f"No variable skip_rds, not skipping any rds.")

    log.info(f"final value of rds_list is {rds_list}")

    for rds in rds_list:
        if force_run == True:
            latest_dump_week_number = 0
        else:
            latest_dump_week_number = rds_list[rds].get('latest_dump_week_number')
        
        if latest_dump_week_number == None:
            latest_dump_week_number = 0
        if rds_list[rds]['is_live'] == True and execution_week_number > int(latest_dump_week_number):
            task_id_list.append('Create_Snapshot_For_RDS_' + rds)
        else:
            log.info(f"Skipping rds {rds} as it has been backed up this week on previous run!")
    # Check if there is any rds for which we need to run the pipeline, we will scale up.
    # if len(task_id_list) > 1:
    #     log.info(f"Scaling up the asg nodes !")
    #     execute_command(["python3", "/opt/airflow/dags/scripts/common/scale_asg.py"],shell=False)
    # else :
    #     log.info(f"NO need of scaling the asg, as there is no rds for taking the snapshots!")
    return task_id_list


# Function to create snapshot for RDS

def _create_snapshot_for_rds(**kwargs):
    rds = kwargs['rds']
    endpoint = kwargs['endpoint']
    snapshot_suffix = "-airflow-snapshot"
    snapshot_name = rds + snapshot_suffix
    _now = str(datetime.utcnow())
    log.info("Executing Python Script for creating a RDS-snapshot..")
    execute_command(["python3", "/opt/airflow/dags/scripts/rds_dump_to_s3/create_rds_snapshot.py",
                    rds, endpoint, snapshot_name], shell=False)
    # This is returned to append to s3 path. This is the time when the snapshot is created
    return _now.replace(' ', 'T').replace(':', '-').split('.')[0]


# Function to select the RDS which are live and their dumps needed to be taken; also acts as a leveller-task for all RDS
def _leveller(**kwargs):
    dumped_task_ids = kwargs['ti'].xcom_pull(task_ids='Select_Required_RDS')
    dumped_task_ids.remove('Leveller')
    task_id_list = []
    for task_id in dumped_task_ids:
        rds = task_id.split('Create_Snapshot_For_RDS_')[1]
        task_id_list.append('Create_Temp_RDS_From_Snapshot_' + rds)
    task_id_list.append('Update_Latest_Dump_Week_Number')
    return task_id_list


# Function to create temporary RDS from snapshot

def _create_temp_rds_from_snapshot(**kwargs):
    rds = kwargs['rds']
    endpoint = kwargs['endpoint']
    subnet_group_name = kwargs['subnet_group_name']
    vpc_security_group_ids = kwargs['vpc_security_group_ids']
    snapshot_suffix = "-airflow-snapshot"
    snapshot_name = rds + snapshot_suffix
    region = endpoint.split(".")[2]
    log.info("Executing Python Script for creating a temp-RDS from a snapshot..")
    args_list = ["python3", "/opt/airflow/dags/scripts/rds_dump_to_s3/restore_from_snapshot.py",
                 snapshot_name, region, subnet_group_name]
    for vpc_security_group_id in vpc_security_group_ids:
        args_list.append(vpc_security_group_id)
    execute_command(args_list, shell=False)
    return read_file("/home/<USER>/temp_rds_endpoint")


# Function to delete RDS-snapshot

def _delete_snapshot(**kwargs):
    rds = kwargs['rds']
    endpoint = kwargs['endpoint']
    snapshot_suffix = "-airflow-snapshot"
    snapshot_name = rds + snapshot_suffix
    region = endpoint.split(".")[2]
    log.info(f"Executing Python Script for deleting RDS-snapshot {snapshot_name}")
    execute_command(["python3", "/opt/airflow/dags/scripts/rds_dump_to_s3/delete_rds_snapshot.py",
                    snapshot_name, region], shell=False)


# Function to fetch the list of all DBs of an RDS

def _get_all_db_list(**kwargs):
    rds = kwargs['rds']
    _task_id = 'Create_Temp_RDS_From_Snapshot_' + rds
    endpoint = kwargs['ti'].xcom_pull(task_ids=_task_id)
    rds_db_list = 'db_list_' + rds
    username_file = "/home/<USER>/postgres-credentials/username"
    password_file = "/home/<USER>/postgres-credentials/password"
    output_file = "/home/<USER>/db_list_" + rds
    log.info("Executing Bash Script for getting list of databases..")
    execute_command(["sh", "/opt/airflow/dags/scripts/rds_dump_to_s3/get-all-dbs.sh",
                    endpoint, username_file, password_file, output_file], shell=False)

    new_db_list = read_file(output_file).strip('\n').split(" ")
    old_db_list_obj = Variable.get(rds_db_list, deserialize_json=True)
    # old_db_list_obj is of type {"available": ['sg1_checkout_and_delivery_controls','older1','older2'], 'unavailable': ['una1','una2']}
    old_db_list = sum(old_db_list_obj.values(),[]) # Creating a list from values ['sg1_checkout_and_delivery_controls', 'older1', 'older2', 'una1', 'una2']
    new_db_list_obj = {}
    new_db_list_obj['available'] = new_db_list
    new_db_list_obj['unavailable'] = list(set(old_db_list)-set(new_db_list))
    Variable.set(rds_db_list, json.dumps(new_db_list_obj))


def _get_all_table_groupings(**kwargs):
    rds = kwargs['rds']
    _task_id = 'Create_Temp_RDS_From_Snapshot_' + rds
    endpoint = kwargs['ti'].xcom_pull(task_ids=_task_id)
    username_file = "/home/<USER>/postgres-credentials/username"
    password_file = "/home/<USER>/postgres-credentials/password"
    rds_db_list = Variable.get('db_list_' + rds)
    rds_db_list_file = "/opt/airflow/rds_db_list_file"
    write_to_file(rds_db_list_file,rds_db_list)
    # structure of rds_db_list is => {"sigma_userside" : {"size": "15864Ki", "is_live": true}, "sigma_cart_and_wishlist" : {"size": "8936Ki", "is_live": true}}
    # Create grouping variable as they will create the structure of downstream tasks
    accessible_table_list_folder = "/opt/airflow/accessible_table_list"
    non_accessible_table_list_folder = "/opt/airflow/non_accessible_table_list"
    non_accessible_tables_all_dbs = "/opt/airflow/non_accessible_tables_all_dbs.txt"
    execute_command(["bash", "/opt/airflow/dags/scripts/rds_dump_to_s3/get_all_tables_and_sizes.sh",
                    endpoint, rds_db_list_file, accessible_table_list_folder, non_accessible_table_list_folder, non_accessible_tables_all_dbs, username_file, password_file], shell=False)

    # Sending mail, if there exists any non_accessible tables in the given rds
    if (os.path.getsize(non_accessible_tables_all_dbs) > 0):
        # TODO: Create the log url and add it in the mail https://devops-pipeline.prod.strawmine.com/graph?dag_id=RDS_Dumps_To_S3&execution_date=2021-09-02T12:29:28.600996+00:00
        title = f"Airflow: {PARENT_DAG_NAME} permission issue "
        body = f"Following tables for rds {rds} are not having correct permissions. Please fix this! Current run skipped these tables"
        send_email(email_group, title, body,files=[non_accessible_tables_all_dbs])
    
    # Creating table groupings on db level
    # _table_groupings_db_rds = 'table_groupings_' + db + '_' + rds
    # capacity should be variable derived
    capacity = '3221225472'    # To be provided in the same units as the table-sizes i.e. Bytes For 3Gi 
    log.info("Executing Python Script for creating table groupings..")
    # final variable will be like [{'localized_string_tuvs': 5944}, {'variant_parameter_choices_to_sub_categories': 5048}, {'facet_choices_to_sub_categories': 3376}, {'facet_choices': 880, 'localized_string_tus': 568, 'size_variant_parameter_choices': 336, 'sub_categories': 200, 'dw_data_sync_status': 56}, {'facets_to_sub_categories': 312, 'variant_parameter_choices': 272, 'external_category_codes': 200, 'variant_parameters_to_sub_categories': 144, 'quantity_units_to_sub_categories': 144, 'facets': 112, 'variant_parameters': 48, 'top_level_categories': 48, 'size_schemes': 48, 'measurement_units': 48, 'color_variant_parameter_choices': 48, 'category_hierarchy_versions': 48, 'categories': 48}]
    execute_command(["python3", "/opt/airflow/dags/scripts/rds_dump_to_s3/bin_creator.py",
                    capacity, accessible_table_list_folder, rds], shell=False)

def _select_required_dbs(**kwargs):
    rds = kwargs['rds']
    rds_db_list = 'db_list_' + rds
    db_list_available = Variable.get(rds_db_list, deserialize_json=True)['available']
    task_id_list = ['Delete_Temp_RDS_' + rds]
    for db in db_list_available:
        task_id_list.append('Create_Persistent_Volumes_' + rds + '_' + db)
    return task_id_list

# Function to create persistent volume claims for all table-groups of a DB

def _create_volumes_for_table_groupings(**kwargs):
    rds = kwargs['rds']
    db = kwargs['db']
    log.info("Executing Python Script for creating volumes for each of the table groups..")
    execute_command(
        """python3 /opt/airflow/dags/scripts/rds_dump_to_s3/create_pvc.py '{0}' '{1}'""".format(rds, db))
    task_id_list = [f"Delete_Allocated_Volumes_{rds}_{db}"]
    _table_grouping_name = 'table_groupings_' + db + '_' + rds
    table_grouping = Variable.get(_table_grouping_name, deserialize_json=True)
    for group_number in range(len(table_grouping)):
        task_id_list.append(f"Avoid_Lazy_Loading_{rds}_{db}_{group_number}")
    return task_id_list


# Function to run count(*) over each table in a group, to avoid lazy-loading

def _avoid_lazy_loading(**kwargs):
    rds = kwargs['rds']
    _task_id = 'Create_Temp_RDS_From_Snapshot_' + rds
    endpoint = kwargs['ti'].xcom_pull(task_ids=_task_id)
    db = kwargs['db']
    group_number= kwargs['group_number']
    _table_groupings_db_rds = 'table_groupings_' + db + '_' + rds
    table_groupings_db_rds = Variable.get(_table_groupings_db_rds, deserialize_json=True)
    table_list_file = f"/opt/snapshot2s3/table_list_{rds}_{db}_{group_number}"
    table_completed_file = f"/opt/snapshot2s3/table_completed_{rds}_{db}_{group_number}"
    with open(table_list_file, "w") as _file:
        for key in table_groupings_db_rds[group_number]:
            print(key, file=_file)
    username_file = "/home/<USER>/postgres-credentials/username"
    password_file = "/home/<USER>/postgres-credentials/password"
    log.info("Executing Bash Script for running count(*) over all tables of the group..")
    execute_command(["sh", "/opt/airflow/dags/scripts/rds_dump_to_s3/run-count-star.sh",
                    db, table_list_file, table_completed_file, endpoint, username_file, password_file], shell=False)


# Function to take PG-Dump of a DB

def _take_pg_dump(**kwargs):
    rds = kwargs['rds']
    _task_id = 'Create_Temp_RDS_From_Snapshot_' + rds
    endpoint = kwargs['ti'].xcom_pull(task_ids=_task_id)
    db = kwargs['db']
    group_number= kwargs['group_number']
    username_file = "/home/<USER>/postgres-credentials/username"
    password_file = "/home/<USER>/postgres-credentials/password"
    table_list_file = f"/opt/snapshot2s3/table_completed_{rds}_{db}_{group_number}"
    dump_metadata_file = f"/opt/snapshot2s3/dump_completed_{rds}_{db}_{group_number}"
    dump_folder = "/opt/snapshot2s3/dump_" + rds + "_" + db
    log.info("Executing Bash Script for taking pg_dump..")
    execute_command(["sh", "/opt/airflow/dags/scripts/rds_dump_to_s3/take-pg-dump.sh",
                    db, endpoint, username_file, password_file, dump_folder,table_list_file,dump_metadata_file], shell=False)


# Function to push Dump to s3

def _push_dump_to_s3(**kwargs):
    rds = kwargs['rds']
    db = kwargs['db']
    _task_id = 'Create_Snapshot_For_RDS_' + rds
    dump_timestamp = kwargs['ti'].xcom_pull(task_ids=_task_id)
    dump_folder = "/opt/snapshot2s3/dump_" + rds + "_" + db
    s3_bucket = Variable.get('s3_bucket')
    log.info("Executing Bash Script for pushing dump to s3..")
    execute_command(["sh", "/opt/airflow/dags/scripts/rds_dump_to_s3/sync-to-s3-cli.sh",
                    rds, db, dump_timestamp, dump_folder, s3_bucket], shell=False)
    return "PUSH_DUMP_S3_SUCCESS"


# Function to delete allocated persistent volume claims

def _delete_allocated_volumes(**kwargs):
    rds = kwargs['rds']
    db = kwargs['db']
    log.info("Executing Python Script for deleting the allocated volumes..")
    execute_command(
        ["python3", "/opt/airflow/dags/scripts/rds_dump_to_s3/delete_pvc.py", rds, db], shell=False)


# Function to delete temporary RDS

def _delete_temp_rds(**kwargs):
    rds = kwargs['rds']
    endpoint = kwargs['endpoint']
    rds_suffix = "-airflow-snapshot"
    rds_name = rds + rds_suffix
    region = endpoint.split(".")[2]
    log.info(f"Executing Python Script for deleting temp-RDS {rds_name}")
    execute_command(["python3", "/opt/airflow/dags/scripts/rds_dump_to_s3/delete_temp_rds.py",rds_name, region], shell=False)


# Function to update the latest dump week numbers for each RDS

def _update_latest_dump_week_number(**kwargs):
    rds_list = Variable.get('rds_list', deserialize_json=True)
    execution_week_number = int(kwargs['ti'].xcom_pull(task_ids='Get_All_RDS_List'))
    dumped_task_ids = kwargs['ti'].xcom_pull(task_ids='Select_Required_RDS')
    dumped_task_ids.remove('Leveller')
    # Get all the rds for which this is run
    for task_id in dumped_task_ids:
        flag = 0
        rds = task_id.split('Create_Snapshot_For_RDS_')[1]
        rds_db_list = 'db_list_' + rds
        db_list = Variable.get(rds_db_list, deserialize_json=True)
        for db in db_list['available']:
            _table_groupings_db_rds = 'table_groupings_' + db + '_' + rds
            table_groupings_db_rds = Variable.get(_table_groupings_db_rds, deserialize_json=True)
            for group_number in range(len(table_groupings_db_rds)):
                _task_id = 'Push_Dump_To_S3_' + rds + '_' + db + '_' + str(group_number)
                dump_status = kwargs['ti'].xcom_pull(task_ids=_task_id)
                if dump_status == None:
                    dump_status = "NO_STATUS_AVAILABLE"
                if dump_status == "PUSH_DUMP_S3_SUCCESS":
                    flag = 1
                    break
            if flag == 1:
                break
        if flag == 1:
            rds_list[rds]['latest_dump_week_number'] = execution_week_number
    Variable.set('rds_list', json.dumps(rds_list))
    log.info("Updated the latest_dump_week_numbers for all dumped RDS..")


######################################################################

# Tasks used in this DAG
# The following part of the code will be run continuously (after fixed intervals) by Airflow, in order to refresh the DAG Structure

#First task: gets all the rds_list
get_all_rds_list = PythonOperator(
    task_id='Get_All_RDS_List',
    dag=dag,
    python_callable=_get_all_rds_list,
    executor_config={
            "pod_override": k8s.V1Pod(
                spec=k8s.V1PodSpec(
                    service_account_name="aws-airflow-snapshot2s3",
                    security_context=k8s.V1PodSecurityContext(
                        fs_group=50000, run_as_group=50000, run_as_user=50000
                    ),
                    containers=[
                        k8s.V1Container(
                            name="base",
                        )
                    ],
                )
            )
    }
)

# This is PythonBranchOperator used to get all the downstream branches which needs to be run.
# This is decided based on the rds status(live) and the week number of the previous task run
# Has All the create_snapshot tasks and leveller task as downstream
select_required_rds = BranchPythonOperator(
    task_id='Select_Required_RDS',
    dag=dag,
    python_callable=_select_required_rds,
    executor_config={
        "pod_override": k8s.V1Pod(
            spec=k8s.V1PodSpec(
                service_account_name="aws-airflow-eks-node-scaler",
                security_context=k8s.V1PodSecurityContext(
                    fs_group=50000, run_as_group=50000, run_as_user=50000
                ),
                containers=[
                    k8s.V1Container(
                        name="base",
                    )
                ],
            )
        )
    }
)

# Has all the create_temp_rds tasks and Update_Latest_Dump_Week_Number as downstream
leveller = BranchPythonOperator(
    task_id='Leveller',
    dag=dag,
    python_callable=_leveller,
    trigger_rule='all_done'
)

update_latest_dump_week_number = PythonOperator(
    task_id='Update_Latest_Dump_Week_Number',
    dag=dag,
    python_callable=_update_latest_dump_week_number,
    trigger_rule='all_done'
)

rds_list = Variable.get('rds_list', default_var='RDS_LIST_DOES_NOT_EXIST')

if rds_list == 'RDS_LIST_DOES_NOT_EXIST':
    Variable.set('rds_list', '{}')

rds_list = Variable.get('rds_list', deserialize_json=True)

# RDS-level tasks (if no RDS is live or needed to be taken dump, then following tasks will be skipped)

# Iterating over the rds_list variable containing info about all the rds 
for rds in rds_list:

    endpoint = rds_list[rds]['endpoint']
    subnet_group_name = rds_list[rds]['subnet_group_name']
    vpc_security_group_ids = rds_list[rds]['vpc_security_group_ids']

    create_rds_snapshot = PythonOperator(
        task_id='Create_Snapshot_For_RDS_' + rds,
        dag=dag,
        python_callable=_create_snapshot_for_rds,
        op_kwargs={'rds': rds, 'endpoint': endpoint, 'subnet_group_name': subnet_group_name,
                   'vpc_security_group_ids': vpc_security_group_ids},
        #pool='create_snapshot_pool',
        executor_config={
            "pod_override": k8s.V1Pod(
                spec=k8s.V1PodSpec(
                    service_account_name="aws-airflow-snapshot2s3",
                    security_context=k8s.V1PodSecurityContext(
                        fs_group=50000, run_as_group=50000, run_as_user=50000
                    ),
                    containers=[
                        k8s.V1Container(
                            name="base",
                        )
                    ],
                )
            )
        }
    )

    create_temp_rds = PythonOperator(
        task_id='Create_Temp_RDS_From_Snapshot_' + rds,
        dag=dag,
        python_callable=_create_temp_rds_from_snapshot,
        op_kwargs={'rds': rds, 'endpoint': endpoint, 'subnet_group_name': subnet_group_name,
                   'vpc_security_group_ids': vpc_security_group_ids},
        #pool='create_rds_pool',
        executor_config={
            "pod_override": k8s.V1Pod(
                spec=k8s.V1PodSpec(
                    service_account_name="aws-airflow-snapshot2s3",
                    security_context=k8s.V1PodSecurityContext(
                        fs_group=50000, run_as_group=50000, run_as_user=50000
                    ),
                    containers=[
                        k8s.V1Container(
                            name="base",
                        )
                    ],
                )
            )
        }
    )

    delete_rds_snapshot = PythonOperator(
        task_id='Delete_Snapshot_' + rds,
        dag=dag,
        python_callable=_delete_snapshot,
        trigger_rule='none_skipped',
        op_kwargs={'rds': rds, 'endpoint': endpoint, 'subnet_group_name': subnet_group_name,
                   'vpc_security_group_ids': vpc_security_group_ids},
        #pool='delete_snapshot_pool',
        executor_config={
            "pod_override": k8s.V1Pod(
                spec=k8s.V1PodSpec(
                    service_account_name="aws-airflow-snapshot2s3",
                    security_context=k8s.V1PodSecurityContext(
                        fs_group=50000, run_as_group=50000, run_as_user=50000
                    ),
                    containers=[
                        k8s.V1Container(
                            name="base",
                        )
                    ],
                )
            )
        }
    )

    # Sets variable db_list_<rds>
    get_all_db_list = PythonOperator(
        task_id='Get_All_DB_List_' + rds,
        dag=dag,
        python_callable=_get_all_db_list,
        op_kwargs={'rds': rds, 'endpoint': endpoint, 'subnet_group_name': subnet_group_name,
                   'vpc_security_group_ids': vpc_security_group_ids},
        executor_config={
            "pod_override": k8s.V1Pod(
                spec=k8s.V1PodSpec(
                    security_context=k8s.V1PodSecurityContext(
                        fs_group=50000, run_as_group=50000, run_as_user=50000
                    ),
                    containers=[
                        k8s.V1Container(
                            name="base",
                            volume_mounts=[
                                k8s.V1VolumeMount(
                                    mount_path="/home/<USER>/postgres-credentials", name="postgres-secret"
                                )
                            ],
                        )
                    ],
                    volumes=[
                        k8s.V1Volume(
                            name="postgres-secret",
                            secret=k8s.V1SecretVolumeSource(secret_name="postgres-credentials-snapshot2s3"),
                        ),
                    ],
                )
            )
        }
        
        #pool='get_db_pool'
    )

    # Create table grouping for all the live dbs (By live it means that the db is still available as the last run)
    create_table_grouping_rds_level = PythonOperator(
        task_id = 'Get_Table_Groupings_RDS_' + rds,
        dag=dag,
        python_callable=_get_all_table_groupings,
        op_kwargs={'rds': rds},
        executor_config={
            "pod_override": k8s.V1Pod(
                spec=k8s.V1PodSpec(
                    security_context=k8s.V1PodSecurityContext(
                        fs_group=50000, run_as_group=50000, run_as_user=50000
                    ),
                    containers=[
                        k8s.V1Container(
                            name="base",
                            volume_mounts=[
                                k8s.V1VolumeMount(
                                    mount_path="/home/<USER>/postgres-credentials", name="postgres-secret"
                                )
                            ],
                        )
                    ],
                    volumes=[
                        k8s.V1Volume(
                            name="postgres-secret",
                            secret=k8s.V1SecretVolumeSource(secret_name="postgres-credentials-snapshot2s3"),
                        ),
                    ],
                )
            )
        }
    )

    select_required_dbs = BranchPythonOperator(
        task_id='Select_Required_DBs_For_' + rds,
        dag=dag,
        python_callable=_select_required_dbs,
        op_kwargs={'rds': rds, 'endpoint': endpoint, 'subnet_group_name': subnet_group_name,
                   'vpc_security_group_ids': vpc_security_group_ids},
        #pool='select_dbs_pool'
    )

    delete_temp_rds = PythonOperator(
        task_id='Delete_Temp_RDS_' + rds,
        dag=dag,
        python_callable=_delete_temp_rds,
        op_kwargs={'rds': rds, 'endpoint': endpoint, 'subnet_group_name': subnet_group_name,
                   'vpc_security_group_ids': vpc_security_group_ids},
        #pool='delete_rds_pool',
        trigger_rule='none_skipped',
        # TODO: trigger rule ??
        executor_config={
            "pod_override": k8s.V1Pod(
                spec=k8s.V1PodSpec(
                    service_account_name="aws-airflow-snapshot2s3",
                    security_context=k8s.V1PodSecurityContext(
                        fs_group=50000, run_as_group=50000, run_as_user=50000
                    ),
                    containers=[
                        k8s.V1Container(
                            name="base",
                        )
                    ],
                )
            )
        }
    )

    rds_db_list = 'db_list_' + rds

    db_list = Variable.get(rds_db_list, default_var='DB_LIST_DOES_NOT_EXIST')

    if db_list == 'DB_LIST_DOES_NOT_EXIST':
        Variable.set(rds_db_list, '{"available":[],"unavailable":[]}')

    db_list = Variable.get(rds_db_list, deserialize_json=True)

    # DB-level tasks (if no DB is live or needed to be taken dump, then following tasks will be skipped)

    for db in db_list['available']:
        
        create_persistent_volumes = BranchPythonOperator(
            task_id='Create_Persistent_Volumes_' + rds + '_' + db,
            dag=dag,
            python_callable=_create_volumes_for_table_groupings,
            op_kwargs={'rds': rds, 'endpoint': endpoint, 'subnet_group_name': subnet_group_name,
                    'vpc_security_group_ids': vpc_security_group_ids, 'db': db},
            executor_config={
                "pod_override": k8s.V1Pod(
                    spec=k8s.V1PodSpec(
                        service_account_name="volume-creation-sa",
                        security_context=k8s.V1PodSecurityContext(
                            fs_group=50000, run_as_group=50000, run_as_user=50000
                        ),
                        containers=[
                            k8s.V1Container(
                                name="base",
                            )
                        ],
                    )
                )
            },
            #pool='create_volume_pool'
        )

        delete_allocated_volumes = PythonOperator(
            task_id=f"Delete_Allocated_Volumes_{rds}_{db}",
            dag=dag,
            python_callable=_delete_allocated_volumes,
            trigger_rule='none_skipped',
            op_kwargs={'rds': rds, 'endpoint': endpoint, 'subnet_group_name': subnet_group_name,
                    'vpc_security_group_ids': vpc_security_group_ids, 'db': db},
            executor_config={
                "pod_override": k8s.V1Pod(
                    spec=k8s.V1PodSpec(
                        service_account_name="volume-creation-sa",
                        security_context=k8s.V1PodSecurityContext(
                            fs_group=50000, run_as_group=50000, run_as_user=50000
                        ),
                        containers=[
                            k8s.V1Container(
                                name="base",
                            )
                        ],
                    )
                )
            },
            #pool='delete_volume_pool'
        )

        table_groupings_db_rds = 'table_groupings_' + db + '_' + rds

        table_group_list = Variable.get(table_groupings_db_rds, default_var='TABLE_GROUP_LIST_DOES_NOT_EXIST')

        if table_group_list == 'TABLE_GROUP_LIST_DOES_NOT_EXIST':
            Variable.set(table_groupings_db_rds, '[]')

        table_group_list = Variable.get(table_groupings_db_rds, deserialize_json=True)

        # Table-group-level tasks (if no table-group is present or needed to be taken dump, then following tasks will be skipped)

        for group_number in range(len(table_group_list)):
            _claim_name = f"{rds}-{db}-airflow-pvc-{group_number}".replace("_", "-").lower()

            avoid_lazy_loading = PythonOperator(
                task_id=f"Avoid_Lazy_Loading_{rds}_{db}_{group_number}",
                dag=dag,
                python_callable=_avoid_lazy_loading,
                op_kwargs={'rds': rds, 'endpoint': endpoint, 'subnet_group_name': subnet_group_name,
                        'vpc_security_group_ids': vpc_security_group_ids, 'db': db, 'group_number': group_number},
                executor_config={
                    "pod_override": k8s.V1Pod(
                        spec=k8s.V1PodSpec(
                            security_context=k8s.V1PodSecurityContext(
                                fs_group=50000, run_as_group=50000, run_as_user=50000
                            ),
                            containers=[
                                k8s.V1Container(
                                    name="base",
                                    volume_mounts=[
                                        k8s.V1VolumeMount(
                                            mount_path="/opt/snapshot2s3", name="kubernetes-extra-vol"
                                        ),
                                        k8s.V1VolumeMount(
                                            mount_path="/home/<USER>/postgres-credentials", name="postgres-secret"
                                        )
                                    ],
                                )
                            ],
                            volumes=[
                                k8s.V1Volume(
                                    name="kubernetes-extra-vol",
                                    persistent_volume_claim=k8s.V1PersistentVolumeClaimVolumeSource(
                                        claim_name=_claim_name)
                                ),
                                k8s.V1Volume(
                                    name="postgres-secret",
                                    secret=k8s.V1SecretVolumeSource(secret_name="postgres-credentials-snapshot2s3"),
                                )
                            ],
                        )
                    )
                },
                #pool='avoid_lazy_loading_pool'
            )

            take_pg_dump = PythonOperator(
                task_id=f"Take_PG_Dump_{rds}_{db}_{group_number}",
                dag=dag,
                python_callable=_take_pg_dump,
                op_kwargs={'rds': rds, 'endpoint': endpoint, 'subnet_group_name': subnet_group_name,
                        'vpc_security_group_ids': vpc_security_group_ids, 'db': db, 'group_number': group_number},
                executor_config={
                    "pod_override": k8s.V1Pod(
                        spec=k8s.V1PodSpec(
                            security_context=k8s.V1PodSecurityContext(
                                fs_group=50000, run_as_group=50000, run_as_user=50000
                            ),
                            containers=[
                                k8s.V1Container(
                                    name="base",
                                    volume_mounts=[
                                        k8s.V1VolumeMount(
                                            mount_path="/opt/snapshot2s3", name="kubernetes-extra-vol"
                                        ),
                                        k8s.V1VolumeMount(
                                            mount_path="/home/<USER>/postgres-credentials", name="postgres-secret"
                                        )
                                    ],
                                )
                            ],
                            volumes=[
                                k8s.V1Volume(
                                    name="kubernetes-extra-vol",
                                    persistent_volume_claim=k8s.V1PersistentVolumeClaimVolumeSource(
                                        claim_name=_claim_name)
                                ),
                                k8s.V1Volume(
                                    name="postgres-secret",
                                    secret=k8s.V1SecretVolumeSource(secret_name="postgres-credentials-snapshot2s3"),
                                ),
                            ],
                        )
                    )
                },
                #pool='take_dump_pool'
            )

            push_dump_to_s3 = PythonOperator(
                task_id=f"Push_Dump_To_S3_{rds}_{db}_{group_number}",
                dag=dag,
                python_callable=_push_dump_to_s3,
                op_kwargs={'rds': rds, 'endpoint': endpoint, 'subnet_group_name': subnet_group_name,
                        'vpc_security_group_ids': vpc_security_group_ids, 'db': db, 'group_number': group_number},
                executor_config={
                    "pod_override": k8s.V1Pod(
                        spec=k8s.V1PodSpec(
                            service_account_name="aws-airflow-snapshot2s3",
                            security_context=k8s.V1PodSecurityContext(
                                fs_group=50000, run_as_group=50000, run_as_user=50000
                            ),
                            containers=[
                                k8s.V1Container(
                                    name="base",
                                    volume_mounts=[
                                        k8s.V1VolumeMount(
                                            mount_path="/opt/snapshot2s3", name="kubernetes-extra-vol"
                                        )
                                    ],
                                )
                            ],
                            volumes=[
                                k8s.V1Volume(
                                    name="kubernetes-extra-vol",
                                    persistent_volume_claim=k8s.V1PersistentVolumeClaimVolumeSource(
                                        claim_name=_claim_name)
                                )
                            ],
                        )
                    )
                },
                #pool='push_dump_pool'
            )

            # Setting downstreams on table-group level 
            create_persistent_volumes.set_downstream(avoid_lazy_loading)
            avoid_lazy_loading.set_downstream(take_pg_dump)
            take_pg_dump.set_downstream(push_dump_to_s3)
            push_dump_to_s3.set_downstream(delete_allocated_volumes)

        #Setting downstreams on db level 
        select_required_dbs.set_downstream(create_persistent_volumes)
        create_persistent_volumes.set_downstream(delete_allocated_volumes)
        delete_allocated_volumes.set_downstream(delete_temp_rds)

    #Setting downstreams on rds level 
    select_required_rds.set_downstream(create_rds_snapshot)
    create_rds_snapshot.set_downstream(leveller)
    leveller.set_downstream(create_temp_rds)
    create_temp_rds.set_downstream(delete_rds_snapshot)
    delete_rds_snapshot.set_downstream(get_all_db_list)
    get_all_db_list.set_downstream(create_table_grouping_rds_level)
    create_table_grouping_rds_level.set_downstream(select_required_dbs)
    select_required_dbs.set_downstream(delete_temp_rds)
    # delete_allocated_volumes.set_downstream(delete_temp_rds)
    delete_temp_rds.set_downstream(update_latest_dump_week_number)

# Setting the order of running the tasks

get_all_rds_list >> select_required_rds >> leveller >> update_latest_dump_week_number


############################  End of code  ############################
#######################################################################
