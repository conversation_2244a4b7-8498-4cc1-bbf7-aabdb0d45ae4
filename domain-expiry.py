from datetime import timedelta
from textwrap import dedent
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.dates import days_ago
from airflow.models import Variable
from airflow.utils.email import send_email
import logging
import subprocess
import sys
import os
import time
from kubernetes.client import models as k8s
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
from utility.bashUtils import execute_command, read_file, write_to_file, get_subscribers

log = logging.getLogger(__name__)

PARENT_DAG_NAME = 'Domain-Expiry'

email_group = get_subscribers(PARENT_DAG_NAME)

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email': email_group,
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(seconds=20)
}

dag = DAG(
    PARENT_DAG_NAME,
    default_args=default_args,
    description='Dag for getting domain expiry info',
    # schedule_interval=None,
    schedule_interval='0 11 * * 1', # Every Monday at 11:00 UTC
    catchup = False,
    max_active_runs = 1,
    start_date=days_ago(40), #Setting this, so that there always a DAG run when the dag is switched on
    )

def _check_domain_expiry(**kwargs):
    # run_id = kwargs['run_id']
    consul_token_file = "/home/<USER>/consul/token"
    output_file = "/opt/airflow/mail_content"
    time.sleep(60)
    execute_command(["bash", "/opt/airflow/dags/scripts/domain-expiry/check_domain_expiry.sh", consul_token_file, output_file], shell=False)
    title = "Airflow: Domain expiry report"
    mail_body = read_file(output_file)
    send_email(email_group, title, mail_body)
    

check_domain_expiry_task = PythonOperator(
    task_id='run_domain_expiry_check_script',
    python_callable=_check_domain_expiry,
    dag=dag,
    executor_config={
        "pod_override": k8s.V1Pod(
            spec=k8s.V1PodSpec(
                security_context=k8s.V1PodSecurityContext(
                    fs_group=50000,run_as_group=50000,run_as_user=50000
                ),
                containers=[
                        k8s.V1Container(
                            name="base",
                            volume_mounts=[
                                k8s.V1VolumeMount(
                                    mount_path="/home/<USER>/consul", name="consul-token"
                                )
                            ],
                        )
                    ],
                volumes=[
                        k8s.V1Volume(
                            name="consul-token",
                            secret=k8s.V1SecretVolumeSource(secret_name="consul-token-domain-expiry"),
                        )
                    ],
            )
        )
    }
)

check_domain_expiry_task