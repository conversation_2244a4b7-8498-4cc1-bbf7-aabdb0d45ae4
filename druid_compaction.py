#Dag for running druid compaction
from airflow.operators.python import <PERSON><PERSON><PERSON><PERSON>, BranchPythonOperator
from airflow.models import Variable
from airflow import DAG
from airflow.utils.dates import days_ago
from datetime import datetime, timedelta
import logging
import requests
import json
import sys
import os
import time
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
from utility.bashUtils import get_subscribers


log = logging.getLogger(__name__)

PARENT_DAG_NAME = "druid_compaction"    

email_group = get_subscribers(PARENT_DAG_NAME)

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email': email_group,
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(seconds=60)
}

dag = DAG(
    PARENT_DAG_NAME,
    default_args=default_args,
    description='Dag to run druid compaction',
    schedule_interval='30 21 * * *', #every 3 am IST
    catchup=False,
    max_active_runs=1,
    start_date=days_ago(2),
    tags=['Devops']
)


druid_endpoint = '***********:8081'
request_headers = {'content-type': 'application/json'}
datetime_today = datetime.utcnow()
date_today = datetime_today.strftime('%Y-%m-%d')


def check_compaction_status(task_id):
    url = 'http://{}/druid/indexer/v1/task/{}/status'.format(druid_endpoint, task_id)
    try:
        res = requests.get(url, headers=request_headers, timeout=300)
    except requests.exceptions.RequestException as e:
        print('Could not connect to druid cordinator')
        raise SystemExit(e)
    data = json.loads(res.text)
    status = data['status']['status']
    return status


def run_compaction(payload):
    url = 'http://{}/druid/indexer/v1/task'.format(druid_endpoint)
    try:
        res = requests.post(url, data=json.dumps(payload), headers=request_headers)
    except requests.exceptions.RequestException as e:
        print('Could not connect to druid cordinator')
        raise SystemExit(e)
    data = json.loads(res.text)
    task_id = data['task']
    print('Compaction task submitted, task id - {}'.format(task_id))
    status = ""
    start = datetime.now()
    while status != "SUCCESS":
        time.sleep(60) #sleep for 5 minutes
        status = check_compaction_status(task_id)
        print("task - {}; status - {}".format(task_id, status))
        if status == "FAILED":
            print('Compaction task {} failed \n'.format(task_id))
            return 1
    end = datetime.now()
    print('Time taken by task {} : {} \n'.format(task_id, (end - start)))
    return 0
    
   
def get_segment_count(api):
    url = 'http://{}/{}'.format(druid_endpoint, api)
    try:
        res = requests.get(url, headers=request_headers, timeout=300)
    except requests.exceptions.RequestException as e:
        print('Could not connect to druid cordinator')
        raise SystemExit(e)
    data = json.loads(res.text)
    segment_count = 0
    for item in data.values():
        segment_count += item['count']
    return segment_count


def druid_compaction_ds(ds, last_date, days_to_iterate, run_type):
    #timestamp variables
    if run_type == "auto":  #if auto, compact upto now-2h segments
        timestamp_string = 'T{}:00:00.000'.format((datetime_today - timedelta(hours = 3)).strftime('%H'))
    else:
        timestamp_string = 'T00:00:00.000'

    failure_count = 0
    for d in range(0, days_to_iterate):
        till_date = last_date - timedelta(days = d) #today - d
        from_date = till_date - timedelta(days = 1) #day before till_date
        till_date_ts = till_date.strftime('%Y-%m-%d') + timestamp_string
        from_date_ts = from_date.strftime('%Y-%m-%d') + timestamp_string

        #check segment count and inititate compaction
        segment_timeline_api = 'druid/coordinator/v1/datasources/{}/intervals/{}_{}?simple'.format(ds, from_date_ts, till_date_ts)
        compaction_api = ""
        segment_count = get_segment_count(segment_timeline_api)
        if segment_count > 24: #run compaction only if segment count is more than 24
            print('Initiating compaction for {}'.format(ds))
            print("from - {} ; to - {}".format(from_date_ts, till_date_ts))
            request_payload = {
                'type': 'compact',
                'dataSource': ds,
                'interval': from_date_ts+'Z/'+till_date_ts+'Z',
                'tuningConfig': {
                    'type': 'index_parallel',
                    'maxRowsPerSegment': 5000000,
                    'maxRowsInMemory': 1000000
                }
            }
            status = run_compaction(request_payload)
            if status:
                failure_count += 1
        if failure_count > 3:
            print('Aborting job. More than 3 compaction task failures')
            sys.exit()


def druid_compactions(**kwargs):
    begin_date = kwargs['dag_run'].conf.get('begin_date', 'begin')
    end_date = kwargs['dag_run'].conf.get('end_date', 'end')
    if begin_date == "begin" and end_date == "end":
        last_date = datetime_today
        days_to_iterate = druid_compaction_params['days_to_iterate'] #get this from varaible
        run_type = "auto"
    else:
        from_date = datetime.strptime(begin_date, '%Y-%m-%d')
        last_date = datetime.strptime(end_date, '%Y-%m-%d')
        days_to_iterate = (last_date - from_date).days
        if from_date >= datetime_today or last_date >= datetime_today or days_to_iterate < 1:
            print('Compaction not allowed for this date range')
            sys.exit()
        run_type = "manual"
    data_sources = ['ds3_analytics', 'ds5_analytics', 'ds6_analytics']
    for ds in data_sources:
        druid_compaction_ds(ds, last_date, days_to_iterate, run_type)


druid_compaction_params = Variable.get('druid_compaction_params', deserialize_json=True)

druid_compaction = PythonOperator(
    task_id='druid_compaction',
    dag=dag,
    execution_timeout=timedelta(hours=4),
    python_callable=druid_compactions
)

druid_compaction


