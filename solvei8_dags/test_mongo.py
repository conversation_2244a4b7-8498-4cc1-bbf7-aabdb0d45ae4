from airflow import DAG
from airflow.models import Variable
from airflow.utils.dates import days_ago
from airflow.providers.cncf.kubernetes.operators.kubernetes_pod import KubernetesPodOperator
from airflow.utils.task_group import TaskGroup
from airflow.operators.python import PythonOperator
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from kubernetes.client import models as k8s
from airflow.models.baseoperator import chain
from utility.slack_alerts import on_failure_callback, on_retry_callback
from datetime import timedelta
import logging
import os
import json
from utility.config_validator import get_validated_config


# todo: validate the create pvc and delete pvc with status code 409 and 201


dag_name = "test_mongodb_full_backup_s3"
cluster_required_keys = ['name', 'mongo_host','mongo_port','auth_database','s3_bucket_name', 's3_backup_folder' , 'mongo_username', 'mongo_password']
cluster_config = get_validated_config(
    dag_name=dag_name,
    required_keys=[],
    cluster_required_keys=cluster_required_keys
)

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": days_ago(1),
    "retries": 1,
    # 'on_failure_callback': on_failure_callback,
    # 'on_retry_callback': on_retry_callback,
    "retry_delay": timedelta(minutes=2),
}

with DAG(
    dag_id=dag_name,
    default_args=default_args,
    schedule_interval='30 21 * * *',
    catchup=False,
    tags=['mongo', 'full_backup', 'kubernetes'],
    description="Full MongoDB backup DAG with PVC creation and cleanup",
) as dag:

    # Task to get sizes and create PVCs for all clusters
    create_all_pvcs = KubernetesPodOperator(
        task_id="create_all_pvcs",
        name="create-all-pvcs",
        namespace="airflow",
        service_account_name="airflow",
        image="bitnami/mongodb:4.4",
        security_context={
        "runAsUser": 0,  # Run as root user
        "runAsGroup": 0  # Run as root group
        },
        env_vars={
        "CLUSTER_CONFIG": json.dumps(cluster_config['clusters'])  # Pass clusters as JSON
        },
        cmds=["sh", "-c"],
        arguments=['''
            apt-get update && apt-get install -y jq
            # Set up required variables
            api_server="https://kubernetes.default.svc"
            service_account_path="/var/run/secrets/kubernetes.io/serviceaccount"
            namespace=$(cat $service_account_path/namespace)
            token=$(cat $service_account_path/token)
            cacert=$service_account_path/ca.crt

            create_pvc() {
                local cluster_name=$1
                local db_size=$2
                   
                # Create PVC JSON
                PVC_JSON=$(cat <<EOF
{
    "kind": "PersistentVolumeClaim",
    "apiVersion": "v1",
    "metadata": {
        "name": "mongo-backup-pvc-$cluster_name",
        "namespace": "$namespace"
    },
    "spec": {
        "accessModes": ["ReadWriteOnce"],
        "resources": {
            "requests": {
                "storage": "${db_size}Gi"
            }
        },
        "storageClassName": "ebs-sc-delete"
    }
}
EOF
)
                # Create PVC using curl
                RESPONSE=$(curl -s --cacert $cacert \
                     --header "Authorization: Bearer $token" \
                     -H 'Accept: application/json' \
                     -H 'Content-Type: application/json' \
                     -X POST $api_server/api/v1/namespaces/$namespace/persistentvolumeclaims \
                     -d "$PVC_JSON")

                # Check if PVC already exists (HTTP 409 Conflict)
                if echo "$RESPONSE" | grep -q '"code":409'; then
                    echo "PVC for $cluster_name already exists, skipping creation"
                    return 0
                # Check if PVC was created successfully
                elif echo "$RESPONSE" | grep -q '"kind": "PersistentVolumeClaim"'; then
                    echo "PVC created successfully for $cluster_name"
                    return 0
                else
                    echo "Failed to create PVC for $cluster_name. Response: $RESPONSE"
                    return 1
                fi
            }

            echo "$CLUSTER_CONFIG" | jq -c '.[]' | while read -r cluster; do
                NAME=$(echo "$cluster" | jq -r '.name')
                HOST=$(echo "$cluster" | jq -r '.mongo_host')
                PORT=$(echo "$cluster" | jq -r '.mongo_port')
                Password=$$(echo "$cluster" | jq -r '.mongo_password')
                Username=$$(echo "$cluster" | jq -r '.mongo_username')
                Auth_DB=$$(echo "$cluster" | jq -r '.auth_database')
                

                echo "Processing cluster: $NAME"
                echo "HOST: $HOST"
                echo "PORT: $PORT"
                echo "Password: $Password"
                echo "Username: $Username"
                echo "Auth_DB: $Auth_DB"
 
                DB_SIZE=$(mongo "mongodb://$HOST:$PORT" --username $Username --password $Password --authenticationDatabase $Auth_DB \
                    --quiet --eval "print((db.adminCommand({listDatabases: 1}).totalSize / (1024*1024*1024)).toFixed(0))")
                echo "DB_SIZE: $DB_SIZE"
                if [ "$DB_SIZE" = "NaN" ]; then
                    echo "DB_SIZE is NaN. Retrying with rs.secondaryOk() and robust error handling..."
                    DB_SIZE=$(mongo "mongodb://$HOST:$PORT" \
                        --username $Username \
                        --password $Password \
                        --authenticationDatabase $Auth_DB \
                        --quiet \
                        --eval "rs.secondaryOk(); \
                                let result = db.adminCommand({listDatabases: 1}); \
                                let totalSizeBytes = 0; \
                                if (result && typeof result.totalSize === 'number') { \
                                    totalSizeBytes = result.totalSize; \
                                } \
                                let totalSizeGB = (totalSizeBytes / (1024 * 1024 * 1024)); \
                                print(totalSizeGB.toFixed(0));"
                    )
                    echo "Retried DB_SIZE: $DB_SIZE"
                fi
                if [ -z "$DB_SIZE" ]; then
                    echo "Failed to fetch DB size for $NAME"
                    exit 1
                fi

                DB_SIZE=$(($DB_SIZE + 1))
                echo "Creating PVC with size ${DB_SIZE}Gi"
                create_pvc "$NAME" "$DB_SIZE"
            done
        '''],
    )

  # Create task groups for mongodump operations
    backup_groups = []  # Store all task groups
    for cluster in cluster_config['clusters']:
        with TaskGroup(group_id=f"backup_{cluster['name']}") as tg:
            
            # Task: Perform MongoDB Backup
            mongodump_task = KubernetesPodOperator(
                task_id=f"run_mongodump_{cluster['name']}",
                name=f"mongodump-{cluster['name']}",
                namespace="airflow",
                do_xcom_push=True,  # Enable XCom pushing
                image="mongo:4.4",
                node_selector={"nodepool": "staging-solvei8-amd-on-demand"},
                # --- Add tolerations here for Karpenter's startupTaints ---
                tolerations=[
                    k8s.V1Toleration(
                        key="lifecycle",
                        operator="Equal",
                        value="on-demand-amd64",
                        effect="NoSchedule",
                    )
                ],
                container_resources=k8s.V1ResourceRequirements(
                    requests={"cpu": "1500m", "memory": "500Mi"},
                    limits={"cpu": "2000m", "memory": "600Mi",},
                ),
                volumes=[
                    k8s.V1Volume(
                        name="backup-storage",
                        persistent_volume_claim=k8s.V1PersistentVolumeClaimVolumeSource(
                            claim_name=f"mongo-backup-pvc-{cluster['name']}"
                        )
                    )
                ],
                volume_mounts=[
                    k8s.V1VolumeMount(
                        name="backup-storage",
                        mount_path="/backup"
                    )
                ],
                cmds=["sh", "-c"],
                arguments=[
                    f'''
                    TIMESTAMP="{{{{ ts_nodash }}}}"
                    BACKUP_DIR="/backup/mongo_backup_$TIMESTAMP"
                    rm -rf /backup/*
                    mkdir -p $BACKUP_DIR

                    mongodump --host={cluster["mongo_host"]} \
                             --port={cluster["mongo_port"]} --username {cluster["mongo_username"]} --password {cluster["mongo_password"]} --authenticationDatabase {cluster["auth_database"]} \
                             --out=$BACKUP_DIR \
                             --gzip

                    echo "Backup completed at $(date)" > "$BACKUP_DIR/backup_info.txt"

                    # Write the backup path to XCom in proper JSON format
                    echo "{{\\"backup_path\\": \\"$BACKUP_DIR\\"}}" > /airflow/xcom/return.json
                    '''
                ],
            )

            create_all_pvcs >> mongodump_task
            
            backup_groups.append(tg)  # Add task group to our list

    # Task to delete all PVCs
    cleanup_pvcs = KubernetesPodOperator(
        task_id="cleanup_pvcs",
        name="cleanup-pvcs",
        namespace="airflow",
        service_account_name='airflow',
        image="bitnami/kubectl:latest",
        cmds=["sh", "-c"],
        env_vars={
        "CLUSTER_CONFIG": json.dumps(cluster_config['clusters'])  # Pass clusters as JSON
        },
        trigger_rule='all_done',
        arguments=['''
            service_account_path="/var/run/secrets/kubernetes.io/serviceaccount"
            namespace=$(cat $service_account_path/namespace)
            echo "$CLUSTER_CONFIG" | jq -r '.[].name' | while read cluster_name; do
                printf "Deleting PVC for cluster: %s\n" "$cluster_name"
                kubectl delete pvc "mongo-backup-pvc-$cluster_name" -n "$namespace" --ignore-not-found=true
            done
            ''' 
        ],
    )

    # Set cleanup_pvcs to run after all backup groups are completed
    chain(backup_groups, cleanup_pvcs)
