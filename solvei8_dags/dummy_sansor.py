from airflow import DAG
from airflow.models import Variable
from airflow.sensors.python import PythonSensor
from airflow.operators.python import PythonOperator
from airflow.utils.dates import days_ago 
from kubernetes.client import models as k8s
from datetime import datetime
import json


def print_start_func():
    print("Start")

def print_end_func():
    print("End")

def check_variable_zero():
    check_var_value = Variable.get("check_var", default_var="1")

    if check_var_value != "0":
        # Append current timestamp to 'check_attempts'
        ts_list_str = Variable.get("check_attempts", default_var="[]")
        ts_list = json.loads(ts_list_str)
        ts_list.append(datetime.utcnow().isoformat())
        Variable.set("check_attempts", json.dumps(ts_list))
        print(f"check_var != '0'. Appended timestamp. Current list: {ts_list}")
        return False

    print("check_var == '0'. Sensor will succeed.")
    return True

with DAG(
    dag_id="simple_python_sensor_dag",
    start_date=days_ago(1),
    schedule_interval=None,
    catchup=False,
    description="DAG with PythonSensor waiting for variable to become 0",
) as dag:

    print_start = PythonOperator(
        task_id="print_start",
        python_callable=print_start_func,
    )

    wait_for_check_var_zero = PythonSensor(
        task_id="wait_for_check_var_zero",
        python_callable=check_variable_zero,
        poke_interval=10,
        timeout=40,  # 5 minutes
        mode="reschedule",
        executor_config={
            "pod_override": k8s.V1Pod(
                spec=k8s.V1PodSpec(
                    containers=[
                        k8s.V1Container(
                            name="base", # <--- TARGET THE BASE CONTAINER
                            resources=k8s.V1ResourceRequirements(
                                requests={"cpu": "50m", "memory": "128Mi"},
                                limits={"cpu": "100m", "memory": "256Mi"},
                            ),
                        )
                    ]
                )
            )
        }

    )

    print_end = PythonOperator(
        task_id="print_end",
        python_callable=print_end_func,
    )

    print_start >> wait_for_check_var_zero >> print_end
