from airflow import DAG
from airflow.models import Variable
from airflow.utils.dates import days_ago
from airflow.providers.cncf.kubernetes.operators.kubernetes_pod import KubernetesPodOperator
from airflow.utils.task_group import TaskGroup
from airflow.operators.python import PythonOperator
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from utility.slack_alerts import on_failure_callback, on_retry_callback
from datetime import timedelta
from kubernetes.client import models as k8s
from airflow.models.baseoperator import chain
import logging
import os
import re
import json
from utility.config_validator import get_validated_config

dag_name = "mongodb_incremental_backup_s3"
cluster_required_keys = ['name', 'mongo_host','mongo_port','oplog_database','oplog_collection','auth_database','s3_bucket_name', 's3_backup_folder' , 'mongo_username', 'mongo_password']
    
cluster_config = get_validated_config(
    dag_name=dag_name,
    required_keys=[],
    cluster_required_keys=cluster_required_keys,
    )

def get_last_timestamp(cluster_name: str) -> tuple:
    """Get the last backup timestamp for a cluster"""
    var_key = f"mongo_last_oplog_timestamp_{cluster_name}"
    last_ts = Variable.get(var_key, default_var=None)
    
    if last_ts:
        match = re.match(r"Timestamp\((\d+),\s*(\d+)\)", last_ts)
        return match.groups() if match else ("0", "1")
    return "0", "1"

def upload_backup_to_s3(**context):
    try:
        """Upload MongoDB backup files to S3"""
        cluster_name = context['cluster_name']
        s3_bucket = context['s3_bucket']
        s3_prefix = context['s3_prefix']
        task_instance = context['task_instance']
        
        # Get backup path from XCom
        xcom_result = task_instance.xcom_pull(
            task_ids=f'backup_{cluster_name}.run_mongodump_{cluster_name}'
        )
        backup_path = xcom_result['backup_path']
        
        if not os.path.exists(backup_path):
            raise FileNotFoundError(f"Backup directory not found: {backup_path}")
        
        s3_hook = S3Hook(aws_conn_id='solvei8-stateful-backups-connection')
        
        # Upload all files maintaining directory structure
        for root, _, files in os.walk(backup_path):
            for file in files:
                local_path = os.path.join(root, file)
                # Get relative path by removing backup_path prefix
                relative_path = local_path.replace(backup_path + '/', '')
                s3_key = f"{s3_prefix}/mongo-incremental-backup-{context['ts_nodash']}/{relative_path}"
                try:
                    s3_hook.load_file(
                        filename=local_path,
                        key=s3_key,
                        bucket_name=s3_bucket,
                        replace=True
                    )
                    logging.info(f"Uploaded to s3://{s3_bucket}/{s3_key}")
                except Exception as e:
                    logging.error(f"Failed to upload {local_path}: {str(e)}")
    except Exception as e:
        logging.error(f"Backup upload failed: {str(e)}")
        raise

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": days_ago(1),
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
}

with DAG(
    dag_id=dag_name,
    default_args=default_args,
    schedule_interval="0 */6 * * *",  # Every 6 hours
    catchup=False,
    tags=["mongodb", "incremental", "s3"],
    description="Incremental MongoDB backup DAG with oplog timestamp tracking",
) as dag:

    # Task to get sizes and create PVCs for all clusters
    create_all_pvcs = KubernetesPodOperator(
        task_id="create_all_pvcs",
        name="create-all-",
        namespace="airflow",
        service_account_name="airflow",
        image="bitnami/mongodb:4.4",
        security_context={
        "runAsUser": 0,  # Run as root user
        "runAsGroup": 0  # Run as root group
        },
        env_vars={
        "CLUSTER_CONFIG": json.dumps(cluster_config['clusters'])  # Pass clusters as JSON
        },
        cmds=["sh", "-c"],
        arguments=['''
            apt-get update && apt-get install -y jq
            # Set up required variables
            api_server="https://kubernetes.default.svc"
            service_account_path="/var/run/secrets/kubernetes.io/serviceaccount"
            namespace=$(cat $service_account_path/namespace)
            token=$(cat $service_account_path/token)
            cacert=$service_account_path/ca.crt

            create_pvc() {
                local cluster_name=$1
                local db_size=$2
                   
                # Create PVC JSON
                PVC_JSON=$(cat <<EOF
{
    "kind": "PersistentVolumeClaim",
    "apiVersion": "v1",
    "metadata": {
        "name": "mongo-incremental-backup-pvc-$cluster_name",
        "namespace": "$namespace"
    },
    "spec": {
        "accessModes": ["ReadWriteOnce"],
        "resources": {
            "requests": {
                "storage": "${db_size}Gi"
            }
        },
        "storageClassName": "ebs-sc-delete"
    }
}
EOF
)
                # Create PVC using curl
                RESPONSE=$(curl -s --cacert $cacert \
                     --header "Authorization: Bearer $token" \
                     -H 'Accept: application/json' \
                     -H 'Content-Type: application/json' \
                     -X POST $api_server/api/v1/namespaces/$namespace/persistentvolumeclaims \
                     -d "$PVC_JSON")

                # Check if PVC already exists (HTTP 409 Conflict)
                if echo "$RESPONSE" | grep -q '"code":409'; then
                    echo "PVC for $cluster_name already exists, skipping creation"
                    return 0
                # Check if PVC was created successfully
                elif echo "$RESPONSE" | grep -q '"kind": "PersistentVolumeClaim"'; then
                    echo "PVC created successfully for $cluster_name"
                    return 0
                else
                    echo "Failed to create PVC for $cluster_name. Response: $RESPONSE"
                    return 1
                fi
            }

            echo "$CLUSTER_CONFIG" | jq -c '.[]' | while read -r cluster; do
                NAME=$(echo "$cluster" | jq -r '.name')
                HOST=$(echo "$cluster" | jq -r '.mongo_host')
                PORT=$(echo "$cluster" | jq -r '.mongo_port')
                Password=$$(echo "$cluster" | jq -r '.mongo_password')
                Username=$$(echo "$cluster" | jq -r '.mongo_username')
                Auth_BD=$$(echo "$cluster" | jq -r '.auth_database')

                echo "Processing cluster: $NAME"

                DB_SIZE=$(mongo "mongodb://$HOST:$PORT" --username $Username --password $Password --authenticationDatabase $Auth_BD \
                    --quiet --eval 'print((db.getSiblingDB("local").getCollection("oplog.rs").stats().size / (1024 * 1024 *1024 )).toFixed(0))')


                if [ -z "$DB_SIZE" ]; then
                    echo "Failed to fetch DB size for $NAME"
                    exit 1
                fi

                DB_SIZE=$(($DB_SIZE + 1))
                echo "Creating PVC with size ${DB_SIZE}Gi"
                create_pvc "$NAME" "$DB_SIZE"
            done
        '''],

    )

    # Create task groups for mongodump operations
    backup_groups = []  # Store all task groups
    for cluster in cluster_config['clusters']:
        with TaskGroup(group_id=f"backup_{cluster['name']}") as tg:
            
            t_value, i_value = get_last_timestamp(cluster['name'])
            # Task: Perform MongoDB Backup
            mongodump_task = KubernetesPodOperator(
                task_id=f"run_mongodump_{cluster['name']}",
                name=f"mongodump-{cluster['name']}",
                namespace="airflow",
                do_xcom_push=True,  # Enable XCom pushing
                image="bitnami/mongodb:4.4",
                security_context=k8s.V1SecurityContext(
                    run_as_user=0,  # Run as root
                ),
                volumes=[
                    k8s.V1Volume(
                        name="backup-storage",
                        persistent_volume_claim=k8s.V1PersistentVolumeClaimVolumeSource(
                            claim_name=f"mongo-incremental-backup-pvc-{cluster['name']}"
                        )
                    )
                ],
                volume_mounts=[
                    k8s.V1VolumeMount(
                        name="backup-storage",
                        mount_path="/backup"
                    )
                ],
                cmds=["sh", "-c"],
                arguments=[
                    f'''
                    set -x
                    TIMESTAMP=$(date +%Y%m%d%H%M%S)
                    BACKUP_PATH="/backup/incremental_{cluster['name']}_$TIMESTAMP"
                    rm -rvf /backup/*
                    mkdir -p "$BACKUP_PATH"
                
                    mongodump --host={cluster["mongo_host"]} \
                        --port {cluster['mongo_port']} --username {cluster['mongo_username']} --password {cluster['mongo_password']} --authenticationDatabase {cluster['auth_database']} \
                        --db {cluster['oplog_database']} \
                        --collection {cluster['oplog_collection']} \
                        --query '{{"ts": {{"$gt": {{"$timestamp": {{"t": {t_value}, "i": {i_value}}}}}}}}}' \
                        --out "$BACKUP_PATH"

                    OPLOG_TS=$(mongo --host {cluster['mongo_host']} \\
                            --port {cluster['mongo_port']} --username {cluster['mongo_username']} --password {cluster['mongo_password']} --authenticationDatabase {cluster['auth_database']} \\
                            --quiet --eval 'printjson(db.getSiblingDB("local").oplog.rs.find().sort({{ $natural: -1 }}).limit(1).toArray()[0].ts)')

                    curl -X POST -H "Content-Type: application/json" -u "admin:admin" \\
                            -d "{{\\"key\\": \\"mongo_last_oplog_timestamp_{cluster['name']}\\", \\"value\\": \\"$OPLOG_TS\\"}}" \\
                            "https://airflow.stage-k8s.strawmine.com/api/v1/variables"

                    # Write the backup path to XCom in proper JSON format
                    echo "{{\\"backup_path\\": \\"$BACKUP_PATH\\"}}" > /airflow/xcom/return.json
                    '''
                ],
            )

            # Task: Upload to S3
            upload_to_s3_task = PythonOperator(
                task_id=f"upload_to_s3_{cluster['name']}",
                python_callable=upload_backup_to_s3,
                op_kwargs={
                    'cluster_name': cluster['name'],
                    's3_bucket': cluster['s3_bucket_name'],
                    's3_prefix': cluster['s3_backup_folder']
                },
                executor_config={
                    "pod_override": k8s.V1Pod(
                        spec=k8s.V1PodSpec(
                            security_context=k8s.V1PodSecurityContext(
                                fs_group=0
                            ),
                            containers=[
                                k8s.V1Container(
                                    name="base",
                                    security_context=k8s.V1SecurityContext(
                                        run_as_user=0
                                    ),
                                    volume_mounts=[
                                        k8s.V1VolumeMount(
                                            name="backup-storage",
                                            mount_path="/backup"
                                        )
                                    ]
                                )
                            ],
                            volumes=[
                                k8s.V1Volume(
                                    name="backup-storage",
                                    persistent_volume_claim=k8s.V1PersistentVolumeClaimVolumeSource(
                                        claim_name=f"mongo-incremental-backup-pvc-{cluster['name']}"
                                    )
                                )
                            ]
                        )
                    )
                }
            )
            # Set dependencies within task group
            create_all_pvcs >> mongodump_task >> upload_to_s3_task
            
            backup_groups.append(tg)  # Add task group to our list

    # Task to delete all PVCs
    cleanup_pvcs = KubernetesPodOperator(
        task_id="cleanup_pvcs",
        name="cleanup-pvcs",
        namespace="airflow",
        service_account_name='airflow',
        is_delete_operator_pod=True,
        image="bitnami/kubectl:latest",
        cmds=["sh", "-c"],
        env_vars={
        "CLUSTER_CONFIG": json.dumps(cluster_config['clusters'])  # Pass clusters as JSON
        },
        trigger_rule='all_done',
        arguments=['''
            service_account_path="/var/run/secrets/kubernetes.io/serviceaccount"
            namespace=$(cat $service_account_path/namespace)
            echo "$CLUSTER_CONFIG" | jq -r '.[].name' | while read cluster_name; do
                printf "Deleting PVC for cluster: %s\n" "$cluster_name"
                kubectl delete pvc "mongo-backup-pvc-$cluster_name" -n "$namespace" --ignore-not-found=true
            done
            # Delete PVCs for all clusters
            ''' 
        ],
    )

    # Set cleanup_pvcs to run after all backup groups are completed
    chain(backup_groups, cleanup_pvcs)
