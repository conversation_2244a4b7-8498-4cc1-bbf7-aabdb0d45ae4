"""DAG for running Druid compaction on multiple clusters."""

from airflow.operators.python import PythonOperator
from airflow.models import Variable
from airflow import DAG
from airflow.utils.dates import days_ago
from airflow.utils.task_group import TaskGroup
from datetime import datetime, timedelta
import logging
import requests
import json
import time
from utility.slack_alerts import on_failure_callback, on_retry_callback
from utility.config_validator import get_validated_config

# Configuration validation
dag_name = "druid_compaction_dag"
cluster_required_keys = ["name", "endpoint", "datasources", "days_to_iterate", "segment_threshold"]

config = get_validated_config(
    dag_name=dag_name,
    required_keys=[],
    cluster_required_keys=cluster_required_keys
)

log = logging.getLogger(__name__)

request_headers = {'content-type': 'application/json'}
datetime_today = datetime.utcnow()

def check_compaction_status(endpoint, task_id):
    """Check the status of a compaction task."""
    url = f'http://{endpoint}/druid/indexer/v1/task/{task_id}/status'
    try:
        res = requests.get(url, headers=request_headers, timeout=300)
        res.raise_for_status()
        data = res.json()
        return data['status']['status']
    except requests.exceptions.RequestException as e:
        log.error(f'Could not connect to druid coordinator: {str(e)}')
        raise

def run_compaction(endpoint, payload):
    """Submit and monitor a compaction task."""
    url = f'http://{endpoint}/druid/indexer/v1/task'
    try:
        res = requests.post(url, json=payload, headers=request_headers)
        res.raise_for_status()
        task_id = res.json()['task']
        log.info(f'Compaction task submitted, task id - {task_id}')
        
        start = datetime.now()
        while True:
            status = check_compaction_status(endpoint, task_id)
            log.info(f"task - {task_id}; status - {status}")
            
            if status == "SUCCESS":
                end = datetime.now()
                log.info(f'Time taken by task {task_id}: {end - start}')
                return True
            elif status == "FAILED":
                log.error(f'Compaction task {task_id} failed')
                return False
                
            time.sleep(60)  # Check status every minute
    except requests.exceptions.RequestException as e:
        log.error(f'Error during compaction: {str(e)}')
        raise

def get_segment_count(endpoint, api):
    """Get the segment count for a given interval."""
    url = f'http://{endpoint}/{api}'
    try:
        res = requests.get(url, headers=request_headers, timeout=300)
        res.raise_for_status()
        data = res.json()
        return sum(item['count'] for item in data.values())
    except requests.exceptions.RequestException as e:
        log.error(f'Could not connect to druid coordinator: {str(e)}')
        raise

def run_cluster_compaction(cluster_config, day_offset):
    """Run compaction for a specific cluster."""
    last_date = datetime.utcnow()
    timestamp_string = f'T{(last_date - timedelta(hours=3)).strftime("%H")}:00:00.000'

    failure_count = 0
    endpoint = cluster_config['endpoint']
    till_date = last_date - timedelta(days=day_offset)
    from_date = till_date - timedelta(days=1)
    till_date_ts = till_date.strftime('%Y-%m-%d') + timestamp_string
    from_date_ts = from_date.strftime('%Y-%m-%d') + timestamp_string

    for ds in cluster_config['datasources']:
        segment_timeline_api = f'druid/coordinator/v1/datasources/{ds}/intervals/{from_date_ts}_{till_date_ts}?simple'
        segment_count = get_segment_count(endpoint, segment_timeline_api)
            
        if segment_count > cluster_config['segment_threshold']:
            log.info(f'Initiating compaction for {ds}')
            log.info(f"from - {from_date_ts} ; to - {till_date_ts}")
                
            request_payload = {
                'type': 'compact',
                'dataSource': ds,
                'interval': f'{from_date_ts}Z/{till_date_ts}Z',
                'tuningConfig': {
                    'type': 'index_parallel',
                    'maxRowsPerSegment': 5000000,
                    'maxRowsInMemory': 1000000
                }
            }
                
            if not run_compaction(endpoint, request_payload):
                failure_count += 1
                if failure_count > 3:
                    raise RuntimeError('Aborting job. More than 3 compaction task failures')

# DAG definition
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=1),
    'on_failure_callback': on_failure_callback,
    'on_retry_callback': on_retry_callback
}

with DAG(
    dag_name,
    default_args=default_args,
    description='DAG to run Druid compaction across multiple clusters',
    schedule_interval='30 21 * * *',  # 3:00 AM IST
    catchup=False,
    max_active_runs=1,
    start_date=days_ago(2),
    tags=['druid', 'compaction']
) as dag:
    
    compaction_groups = []  # Store all task groups
    for cluster in config['clusters']:
        # Create a task group for each cluster
        with TaskGroup(group_id=f"compaction_{cluster['name']}") as tg:
            for day_offset in range(cluster['days_to_iterate']):
                task_id = f"compaction_{cluster['name']}_day_{day_offset}_for_all_datasources"
                PythonOperator(
                    task_id=task_id,
                    python_callable=run_cluster_compaction,
                    op_kwargs={'cluster_config': cluster, 'day_offset': day_offset},
                    execution_timeout=timedelta(hours=4)
                )
        
        compaction_groups.append(tg)

# Add DAG documentation
dag.doc_md = """
# Druid Compaction DAG

## Overview
This DAG manages Druid compaction tasks across multiple clusters. It runs daily at 3:00 AM IST.

## Configuration
The DAG expects a configuration in Airflow variables with the following structure:
```json
{
    "clusters": [
        {
            "name": "prod",
            "endpoint": "druid-prod:8081",
            "datasources": ["ds3_analytics", "ds5_analytics", "ds6_analytics"],
            "days_to_iterate": 7,
            "segment_threshold": 24
        },
        {
            "name": "staging",
            "endpoint": "druid-staging:8081",
            "datasources": ["ds3_analytics", "ds5_analytics"],
            "days_to_iterate": 5,
            "segment_threshold": 20
        }
    ]
}
```
"""
