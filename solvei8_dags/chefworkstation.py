from airflow import DAG
from airflow.providers.cncf.kubernetes.operators.kubernetes_pod import KubernetesPodOperator
from kubernetes.client import models as k8s
from airflow.utils.dates import days_ago
from datetime import timedelta
from utility.slack_alerts import on_failure_callback, on_retry_callback

# Default arguments
default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": days_ago(1),
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": on_failure_callback,
    "on_retry_callback": on_retry_callback,
}

# DAG definition
with DAG(
    'chef_workstation_task',
    default_args=default_args,
    schedule_interval="@daily",
    description='Run Chef Workstation tasks in Kubernetes with existing ConfigMap and Secret',
    catchup=False,
    tags=['chef', 'infrastructure'],
) as dag:
    # Volume and PVC configuration for temporary storage
    pvc_name = "chef-temp-pvc"
    
    # Create PVC task
    create_pvc = KubernetesPodOperator(
        task_id="create_temp_pvc",
        name="create-pvc",
        namespace="airflow",
        image="bitnami/kubectl:latest",
        cmds=["sh", "-c"],
        arguments=["""
            cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: chef-temp-pvc
  namespace: airflow
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
  storageClassName: ebs-sc
EOF
        """],
        service_account_name='airflow',
        is_delete_operator_pod=True,
        dag=dag
    )

    # Delete PVC task
    delete_pvc = KubernetesPodOperator(
        task_id="delete_temp_pvc",
        name="delete-pvc",
        namespace="airflow",
        image="bitnami/kubectl:latest",
        cmds=["kubectl"],
        arguments=["delete", "pvc", pvc_name, "-n", "airflow"],
        service_account_name='airflow',
        is_delete_operator_pod=True,
        dag=dag
    )

    # Volume and volume mounts configuration
    volume_mounts = [
        k8s.V1VolumeMount(
            name='ssh-key',
            mount_path='/home/<USER>/.ssh',
            read_only=True
        ),
        k8s.V1VolumeMount(
            name='pod-template',
            mount_path='/opt/airflow/pod_templates',
            read_only=True
        ),
        k8s.V1VolumeMount(
            name='temp-storage',
            mount_path='/tmp/chef',
            read_only=False
        )
    ]

    volumes = [
        # Volume for SSH key from existing secret
        k8s.V1Volume(
            name='ssh-key',
            secret=k8s.V1SecretVolumeSource(
                secret_name='airflow-ssh-secret',
                default_mode=0o600
            )
        ),
        # Volume for existing pod template ConfigMap
        k8s.V1Volume(
            name='pod-template',
            config_map=k8s.V1ConfigMapVolumeSource(
                name='airflow-pod-template'
            )
        ),
        # Volume for temporary storage
        k8s.V1Volume(
            name='temp-storage',
            persistent_volume_claim=k8s.V1PersistentVolumeClaimVolumeSource(
                claim_name=pvc_name
            )
        )
    ]

    # Task using Chef Workstation image
    chef_task = KubernetesPodOperator(
        task_id='run_chef_workstation',
        name='chef-workstation-pod',
        namespace='airflow',
        image='chef/chefworkstation:latest',
        cmds=['sh', '-c'],
        arguments=[
            """
            # Print SSH key info (safely)
            echo "=== SSH Key Information ===" && \
            ls -l /home/<USER>/.ssh && \
            cat /home/<USER>/.ssh/* && \
            
            # Print Pod Template content
            echo "=== Pod Template Content ===" && \
            ls -l /opt/airflow/pod_templates && \
            cat /opt/airflow/pod_templates/* && \
            
            # Verify Chef installation
            echo "=== Chef Version Information ===" && \
            chef -v && \
            knife --version && \
            
            mkdir test && \
            touch test/test.txt && \
            echo ${AWS_ACCESS_KEY_ID} && \
            echo ${AWS_SECRET_ACCESS_KEY} && \
            echo "=== Configuration Complete ==="

            """
        ],
        volumes=volumes,
        volume_mounts=volume_mounts,
        env_vars=[
            k8s.V1EnvVar(
                name='AWS_ACCESS_KEY_ID',
                value_from=k8s.V1EnvVarSource(
                    secret_key_ref=k8s.V1SecretKeySelector(
                        name='airflow-s3-logging-secret',
                        key='AWS_ACCESS_KEY_ID'
                    )
                )
            ),
            k8s.V1EnvVar(
                name='AWS_SECRET_ACCESS_KEY',
                value_from=k8s.V1EnvVarSource(
                    secret_key_ref=k8s.V1SecretKeySelector(
                        name='airflow-s3-logging-secret',
                        key='AWS_SECRET_ACCESS_KEY'
                    )
                )
            ),
        ],
        service_account_name='airflow',
        dag=dag  
    )

    # Verify contents task
    verify_contents = KubernetesPodOperator(
        task_id="verify_test_contents",
        name="verify-contents",
        namespace="airflow",
        image="busybox:latest",
        cmds=["sh", "-c"],
        arguments=[
            """
            echo "=== Listing contents of /test directory ===" && \
            ls -la /test && \
            echo "=== Contents of files in /test ===" && \
            for file in /test/*; do
                if [ -f "$file" ]; then
                    echo "Contents of $file:" && \
                    cat "$file"
                    echo "------------------------"
                fi
            done
            """
        ],
        volumes=[
            k8s.V1Volume(
                name='temp-storage',
                persistent_volume_claim=k8s.V1PersistentVolumeClaimVolumeSource(
                    claim_name=pvc_name
                )
            )
        ],
        volume_mounts=[
            k8s.V1VolumeMount(
                name='temp-storage',
                mount_path='/test',
                sub_path='test'  # This ensures we're mounting the /test subdirectory
            )
        ],
        service_account_name='airflow',
        dag=dag
    )

    # Update task dependencies to include PVC deletion
    create_pvc >> chef_task >> verify_contents >> delete_pvc
