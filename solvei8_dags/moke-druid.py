from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.dates import days_ago
from airflow.utils.task_group import TaskGroup
from datetime import datetime, timedelta
import logging

log = logging.getLogger(__name__)

# Mock segment count API
def get_segment_count(endpoint, api):
    # Always return a mock count
    return 30  # Mock: assume segments are always over threshold

# Mock compaction submitter
def run_compaction(endpoint, payload):
    log.info(f"[MOCK] Submitted compaction for {payload['dataSource']} at {payload['interval']}")
    return True  # Mock: always successful

def run_daywise_compaction(cluster_config, day_offset, **kwargs):
    """Mock compaction logic per day."""
    endpoint = cluster_config['endpoint']
    datasources = cluster_config['datasources']
    threshold = cluster_config['segment_threshold']
    
    today = datetime.utcnow()
    till_date = today - timedelta(days=day_offset)
    from_date = till_date - timedelta(days=1)
    
    from_ts = from_date.strftime('%Y-%m-%dT00:00:00.000')
    till_ts = till_date.strftime('%Y-%m-%dT00:00:00.000')
    
    for ds in datasources:
        segment_count = get_segment_count(endpoint, f"/mock/api/{ds}")
        if segment_count > threshold:
            log.info(f"[MOCK] Compaction triggered for {ds} from {from_ts} to {till_ts}")
            payload = {
                'type': 'compact',
                'dataSource': ds,
                'interval': f"{from_ts}Z/{till_ts}Z",
                'tuningConfig': {
                    'type': 'index_parallel',
                    'maxRowsPerSegment': 5000000,
                    'maxRowsInMemory': 1000000
                }
            }
            run_compaction(endpoint, payload)
        else:
            log.info(f"[MOCK] Skipping {ds}, segment count = {segment_count}")

# Mock config
mock_config = {
    "clusters": [
        {
            "name": "mock_cluster_1",
            "endpoint": "mock-druid-1:8081",
            "datasources": ["ds1", "ds2"],
            "days_to_iterate": 3,
            "segment_threshold": 25
        }
    ]
}

default_args = {
    'owner': 'airflow',
    'start_date': days_ago(1),
    'retries': 0
}

with DAG(
    "mock_druid_daywise_compaction",
    default_args=default_args,
    schedule_interval=None,
    catchup=False,
    tags=["mock", "druid", "compaction"]
) as dag:

    for cluster in mock_config['clusters']:
        with TaskGroup(group_id=f"compaction_{cluster['name']}") as tg:
            for day in range(cluster['days_to_iterate']):
                PythonOperator(
                    task_id=f"day_{day+1}_compaction",
                    python_callable=run_daywise_compaction,
                    op_kwargs={
                        'cluster_config': cluster,
                        'day_offset': day
                    }
                )
