from airflow import DAG
from airflow.models import Variable
from airflow.utils.dates import days_ago
from airflow.providers.cncf.kubernetes.operators.kubernetes_pod import KubernetesPodOperator
from airflow.utils.task_group import TaskGroup
from airflow.operators.python import PythonOperator
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from kubernetes.client import models as k8s
from airflow.models.baseoperator import chain
from utility.slack_alerts import on_failure_callback, on_retry_callback
from datetime import timedelta
import logging
import os
import boto3
import json
from utility.config_validator import get_validated_config

dag_name = "mongodb_restore_incremental_backup_s3"
cluster_required_keys = ['name', 'MONGODB_HOST_ADDRESS', 'MONGODB_PORT_NUMBER', 'MONGODB_AUTH_DATABASE', 
                        'S3_BACKUP_BUCKET_NAME', 'S3_FULL_BACKUP_PREFIX_FOLDER', 'username', 'password', 'RESTORE_DATE', "CLUSTER_SIZE"]
cluster_config = get_validated_config(
    dag_name=dag_name,
    required_keys=[],
    cluster_required_keys=cluster_required_keys
)


def download_backup_from_s3(**context):
    try:
        """Download MongoDB backup files from S3"""
        cluster_name = context['cluster_name']
        s3_bucket = context['s3_bucket']
        s3_prefix = context['s3_prefix']
        restore_date = context['restore_date']
        backup_dir = context['backup_dir']
        
        # Get AWS credentials from Airflow Variable
        aws_credentials = Variable.get("aws_access_credentials", deserialize_json=True)
        
        # Create the backup directory
        os.makedirs(backup_dir, exist_ok=True)
        
        # Initialize S3 client with credentials from Airflow Variable
        s3 = boto3.resource(
            's3',
            aws_access_key_id=aws_credentials['aws_access_key_id'],
            aws_secret_access_key=aws_credentials['aws_secret_access_key'],
            region_name=aws_credentials['region_name']
        )
        
        prefix = f"{s3_prefix}/{restore_date}"
        
        # List and download objects
        objects = s3.Bucket(s3_bucket).objects.filter(Prefix=prefix)
        
        for obj in objects:
            path = os.path.join(backup_dir, os.path.relpath(obj.key, prefix))
            os.makedirs(os.path.dirname(path), exist_ok=True)
            s3.Bucket(s3_bucket).download_file(obj.key, path)
            logging.info(f"Downloaded {obj.key} to {path}")
        
        return {"backup_dir": backup_dir}

    except Exception as e:
        logging.error(f"Backup download failed: {str(e)}")
        raise

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": days_ago(1),
    "retries": 1,
    'on_failure_callback': on_failure_callback,
    'on_retry_callback': on_retry_callback,
    "retry_delay": timedelta(minutes=5),
}

with DAG(
    dag_id=dag_name,
    default_args=default_args,
    schedule_interval=None,  # Manual trigger only
    catchup=False,
    tags=['mongo', 'restore', 'kubernetes'],
    description="MongoDB restore DAG to restore backups from S3",
) as dag:

    # Task to get sizes and create PVCs for all clusters
    create_all_pvcs = KubernetesPodOperator(
        task_id="create_all_pvcs",
        name="create-all-pvcs",
        namespace="airflow",
        service_account_name="airflow",
        image="bitnami/mongodb:4.4",
        cmds=["sh", "-c"],
        arguments=['''
            # Set up required variables
            APISERVER="https://kubernetes.default.svc"
            SERVICEACCOUNT="/var/run/secrets/kubernetes.io/serviceaccount"
            NAMESPACE=$(cat $SERVICEACCOUNT/namespace)
            TOKEN=$(cat $SERVICEACCOUNT/token)
            CACERT=$SERVICEACCOUNT/ca.crt

            create_pvc() {
                local cluster_name=$1
                local cluster_size=$2
                   
                # Create PVC JSON
                PVC_JSON=$(cat <<EOF
{
    "kind": "PersistentVolumeClaim",
    "apiVersion": "v1",
    "metadata": {
        "name": "mongo-restore-pvc-$cluster_name",
        "namespace": "$NAMESPACE"
    },
    "spec": {
        "accessModes": ["ReadWriteOnce"],
        "resources": {
            "requests": {
                "storage": "${cluster_size}Gi"
            }
        },
        "storageClassName": "ebs-sc-delete"
    }
}
EOF
)
                # Create PVC using curl
                RESPONSE=$(curl -s --cacert $CACERT \
                     --header "Authorization: Bearer $TOKEN" \
                     -H 'Accept: application/json' \
                     -H 'Content-Type: application/json' \
                     -X POST $APISERVER/api/v1/namespaces/$NAMESPACE/persistentvolumeclaims \
                     -d "$PVC_JSON")

                # Check if PVC already exists (HTTP 409 Conflict)
                if echo "$RESPONSE" | grep -q '"code":409'; then
                    echo "PVC for $cluster_name already exists, skipping creation"
                    return 0
                # Check if PVC was created successfully
                elif echo "$RESPONSE" | grep -q '"kind": "PersistentVolumeClaim"'; then 
                    echo "PVC created successfully for $cluster_name"
                    return 0
                else
                    echo "Failed to create PVC for $cluster_name. Response: $RESPONSE"
                    return 1
                fi
            }

            ''' + '\n'.join([f'''
            echo "Processing cluster {cluster['name']}..."
            
            create_pvc "{cluster['name']}" "{cluster['CLUSTER_SIZE']}" || exit 1
            ''' for cluster in cluster_config['clusters']])
        ],
    )



    # Create task groups for restore operations
    restore_groups = []
    for cluster in cluster_config['clusters']:
        with TaskGroup(group_id=f"restore_{cluster['name']}") as tg:

            # Task to download backup from S3
            download_task = PythonOperator(
                task_id=f"download_backup_{cluster['name']}",
                python_callable=download_backup_from_s3,
                op_kwargs={
                    'cluster_name': cluster['name'],
                    's3_bucket': cluster['S3_BACKUP_BUCKET_NAME'],
                    's3_prefix': cluster['S3_FULL_BACKUP_PREFIX_FOLDER'],
                    'restore_date': cluster['RESTORE_DATE'],
                    'backup_dir': f"/backup/restore_{cluster['name']}"
                },
                executor_config={
                    "pod_override": k8s.V1Pod(
                        spec=k8s.V1PodSpec(
                            containers=[
                                k8s.V1Container(
                                    name="base",
                                    volume_mounts=[
                                        k8s.V1VolumeMount(
                                            name="restore-storage",
                                            mount_path="/backup"
                                        )
                                    ]
                                )
                            ],
                            volumes=[
                                k8s.V1Volume(
                                    name="restore-storage",
                                    persistent_volume_claim=k8s.V1PersistentVolumeClaimVolumeSource(
                                        claim_name=f"mongo-restore-pvc-{cluster['name']}"
                                    )
                                )
                            ]
                        )
                    )
                }
            )

            # Task to restore MongoDB
            restore_task = KubernetesPodOperator(
                task_id=f"restore_mongodb_{cluster['name']}",
                name=f"mongorestore-{cluster['name']}",
                namespace="airflow",
                image="mongo:4.4",
                cmds=["sh", "-c"],
                arguments=[f'''
                    BACKUP_DIR="/backup/restore_{cluster['name']}"
                    ls -la $BACKUP_DIR
                    mongorestore --host={cluster['MONGODB_HOST_ADDRESS']} --port={cluster['MONGODB_PORT_NUMBER']} --oplogReplay $BACKUP_DIR 

                    mongo --host={cluster['MONGODB_HOST_ADDRESS']} --port={cluster['MONGODB_PORT_NUMBER']} --quiet --eval "print((db.adminCommand({{listDatabases: 1}}).totalSize / (1024*1024*1024)).toFixed(0))"
                    
                    echo "MongoDB restore completed for {cluster['name']}"
                '''],
                volumes=[
                    k8s.V1Volume(
                        name="restore-storage",
                        persistent_volume_claim=k8s.V1PersistentVolumeClaimVolumeSource(
                            claim_name=f"mongo-restore-pvc-{cluster['name']}"
                        )
                    )
                ],
                volume_mounts=[
                    k8s.V1VolumeMount(
                        name="restore-storage",
                        mount_path="/backup"
                    )
                ],
            )

            create_all_pvcs >> download_task >> restore_task
            
            restore_groups.append(tg)


# Task to cleanup PVC
cleanup_pvc = KubernetesPodOperator(
    task_id=f"cleanup_pvc_{cluster['name']}",
    name=f"cleanup-pvc-{cluster['name']}",
    namespace="airflow",
    service_account_name='airflow',
    image="bitnami/kubectl:latest",
    cmds=["sh", "-c"],
    trigger_rule='always',
    arguments=['''
        # Delete PVCs for all clusters
        ''' + '\n'.join([
            f'kubectl delete pvc mongo-restore-pvc-{cluster["name"]} -n airflow --ignore-not-found=true'
            for cluster in cluster_config['clusters']
        ])
    ],
)
            # Set task dependencies within group
chain(restore_groups, cleanup_pvc)
