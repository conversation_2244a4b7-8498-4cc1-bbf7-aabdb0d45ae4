from airflow import DAG
from datetime import datetime, timedelta
from airflow.providers.ssh.operators.ssh import SSHOperator
# from airflow.utils.dates import days_ago
from airflow.models import Variable
from airflow.utils.dates import days_ago
from utility.slack_alerts import on_failure_callback, on_retry_callback

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": days_ago(1),
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    # "on_failure_callback": on_failure_callback,
    # "on_retry_callback": on_retry_callback,
}

# Define DAG
dag = DAG(
    "ssh_test_dag",
    default_args=default_args,
    schedule_interval="@daily",  # Run manually
    catchup=False, 
    description="create ssh connection and run the command",
)

# SSH Task: Runs "date" on the remote server
ssh_task = SSHOperator(
    task_id="test_ssh_connection",
    ssh_conn_id="my_ssh_connection",  # Airflow Connection ID
    command=Variable.get("docker_exec_command", default_var="docker exec metabase-mariadb ls /"),
    # command="sudo bash {{ params.script_path }}",
    # params={"script_path": "/home/<USER>/get_info.sh"},
    dag=dag,
    cmd_timeout=300, 
)

ssh_task  # Task execution

