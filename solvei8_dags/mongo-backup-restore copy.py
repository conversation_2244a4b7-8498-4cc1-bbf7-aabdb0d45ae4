from airflow import DAG
from airflow.models import Variable
from airflow.utils.dates import days_ago
from airflow.providers.cncf.kubernetes.operators.kubernetes_pod import KubernetesPodOperator
from airflow.utils.task_group import TaskGroup
from airflow.operators.python import <PERSON><PERSON><PERSON>ator , BranchPythonOperator
from airflow.operators.dummy import DummyOperator
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from kubernetes.client import models as k8s
from airflow.models.baseoperator import chain
from utility.slack_alerts import on_failure_callback, on_retry_callback
from datetime import timedelta, datetime
import logging
import os
import json
from utility.config_validator import get_validated_config


# todo: validate the create pvc and delete pvc with status code 409 and 201


dag_name = "mongo_backup_restore_s3"
cluster_required_keys = ['name', 'mongo_host','mongo_port','auth_database','s3_bucket_name', 's3_backup_folder' , 'mongo_username', 'mongo_password']
cluster_config = get_validated_config(
    dag_name=dag_name,
    required_keys=[],
    cluster_required_keys=cluster_required_keys
)

def compare_restore_counts(**context):
    try:
        cluster_name = context['cluster_name']
        ti = context['ti']

        # Pull document counts from before the backup
        pre_restore_task_id = f"backup_{cluster_name}.check_collections_{cluster_name}"
        pre_restore_data = ti.xcom_pull(task_ids=pre_restore_task_id)

        # Pull document counts from after the restore
        post_restore_task_id = f"backup_{cluster_name}.restore_mongodb_{cluster_name}"
        post_restore_data = ti.xcom_pull(task_ids=post_restore_task_id)

        # Check if XCom data was pulled successfully
        if not pre_restore_data or not post_restore_data:
            raise ValueError(f"Failed to pull XCom data for {cluster_name}. Pre-restore data: {pre_restore_data}, Post-restore data: {post_restore_data}")

        # Convert db_sizes lists to dictionaries for easier lookup
        pre_db_map = {db['database']: db['totalDocuments'] for db in pre_restore_data['db_sizes']}
        post_db_map = {db['database']: db['totalDocuments'] for db in post_restore_data['db_sizes']}

        logging.info(f"--- Document Count Verification Report for {cluster_name} ---")

        # Compare Grand Total Documents
        pre_grand_total = pre_restore_data['grand_total_documents']
        post_grand_total = post_restore_data['grand_total_documents']
        grand_total_diff = post_grand_total - pre_grand_total
        grand_total_percent_diff = (grand_total_diff / pre_grand_total) * 100 if pre_grand_total > 0 else 0

        logging.info(f"Grand Total Documents (Before Restore): {pre_grand_total:,}")
        logging.info(f"Grand Total Documents (After Restore): {post_grand_total:,}")
        logging.info(f"Difference: {grand_total_diff:,} ({grand_total_percent_diff:.2f}%)")
        if grand_total_diff != 0:
            logging.warning("Grand total document count mismatch!")

        # Compare individual databases
        db_report = []
        all_db_names = set(pre_db_map.keys()) | set(post_db_map.keys())

        for db_name in sorted(list(all_db_names)):
            pre_count = pre_db_map.get(db_name, 0)
            post_count = post_db_map.get(db_name, 0)
            diff = post_count - pre_count
            percent_diff = (diff / pre_count) * 100 if pre_count > 0 else 0

            report_entry = {
                "database": db_name,
                "before_restore": pre_count,
                "after_restore": post_count,
                "difference": diff,
                "percent_difference": f"{percent_diff:.2f}%"
            }
            db_report.append(report_entry)

            if diff != 0:
                logging.warning(f"Database '{db_name}': Mismatch found! Before: {pre_count:,}, After: {post_count:,}, Diff: {diff:,} ({percent_diff:.2f}%)")
            else:
                logging.info(f"Database '{db_name}': Counts match ({pre_count:,})")

        # You can also push the full report to XCom if needed for later tasks
        # ti.xcom_push(key="restore_verification_report", value=db_report)
        logging.info(db_report)

        logging.info("--- End of Report ---")

    except Exception as e:
        logging.error(f"Error during restore count comparison for cluster {cluster_name}: {e}")
        raise

def upload_backup_to_s3(**context):
    try:
        """Upload MongoDB backup files to S3"""
        cluster_name = context['cluster_name']
        s3_bucket = context['s3_bucket']
        s3_prefix = context['s3_prefix']
        task_instance = context['task_instance']
        
        # Get backup path from XCom
        xcom_result = task_instance.xcom_pull(
            task_ids=f'backup_{cluster_name}.run_mongodump_{cluster_name}'
        )
        backup_path = xcom_result['backup_path']
        
        if not os.path.exists(backup_path):
            raise FileNotFoundError(f"Backup directory not found: {backup_path}")
        
        s3_hook = S3Hook(aws_conn_id='solvei8-stateful-backups-connection')
        print("upload done 👍")
        # Upload all files maintaining directory structure
        for root, _, files in os.walk(backup_path):
            for file in files:
                local_path = os.path.join(root, file)
                # Get relative path by removing backup_path prefix
                relative_path = local_path.replace(backup_path + '/', '')
                s3_key = f"{s3_prefix}/mongo-full-backup-{context['ts_nodash']}/{relative_path}"
                try: 
                    s3_hook.load_file(
                        filename=local_path,
                        key=s3_key,
                        bucket_name=s3_bucket,
                        replace=True
                    )
                    logging.info(f"Uploaded to s3://{s3_bucket}/{s3_key}")
                except Exception as e:
                    logging.error(f"Failed to upload {local_path}: {str(e)}")
    except Exception as e:
        logging.error(f"Backup upload failed: {str(e)}")
        raise

# This function replaces the original complex 'branch_based_on_monday'
def branch_based_on_monday(**context):
    """Branches the DAG based on the day of the week."""
    tg_prefix = context['params']['tg_prefix']
    cluster_name = context['params']['cluster_name']
    # Monday = 0
    if context['execution_date'].weekday() == 1:
        return f"{tg_prefix}.check_collections_{cluster_name}"
    else:
        return f"{tg_prefix}.dummy_monday_restore_skip_{cluster_name}"


default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": days_ago(1),
    "retries": 2,
    # 'on_failure_callback': on_failure_callback,
    # 'on_retry_callback': on_retry_callback,
    "retry_delay": timedelta(minutes=5),
}

with DAG(
    dag_id=dag_name,
    default_args=default_args,
    schedule_interval='30 21 * * *',
    catchup=False,
    tags=['mongo', 'full_backup', 'kubernetes'],
    description="Full MongoDB backup DAG with PVC creation and cleanup",
) as dag:

    # Task to get sizes and create PVCs for all clusters
    create_all_pvcs = KubernetesPodOperator(
        task_id="create_all_pvcs",
        name="create-all-pvcs",
        namespace="airflow",
        service_account_name="airflow",
        image="************.dkr.ecr.ap-southeast-1.amazonaws.com/madhav01/mongodb:4.4",
        security_context={
        "runAsUser": 0,  # Run as root user
        "runAsGroup": 0  # Run as root group
        },
        env_vars={
        "CLUSTER_CONFIG": json.dumps(cluster_config['clusters'])  # Pass clusters as JSON
        },
        cmds=["sh", "-c"],
        arguments=['''
            # Set up required variables
            api_server="https://kubernetes.default.svc"
            service_account_path="/var/run/secrets/kubernetes.io/serviceaccount"
            namespace=$(cat $service_account_path/namespace)
            token=$(cat $service_account_path/token)
            cacert=$service_account_path/ca.crt

            create_pvc() {
                local cluster_name=$1
                local db_size=$2
                   
                # Create PVC JSON
                PVC_JSON=$(cat <<EOF
{
    "kind": "PersistentVolumeClaim",
    "apiVersion": "v1",
    "metadata": {
        "name": "$cluster_name",
        "namespace": "$namespace"
    },
    "spec": {
        "accessModes": ["ReadWriteOnce"],
        "resources": {
            "requests": {
                "storage": "${db_size}Gi"
            }
        },
        "storageClassName": "ebs-sc-delete"
    }
}
EOF
)
                # Create PVC using curl
                RESPONSE=$(curl -s --cacert $cacert \
                     --header "Authorization: Bearer $token" \
                     -H 'Accept: application/json' \
                     -H 'Content-Type: application/json' \
                     -X POST $api_server/api/v1/namespaces/$namespace/persistentvolumeclaims \
                     -d "$PVC_JSON")

                # Check if PVC already exists (HTTP 409 Conflict)
                if echo "$RESPONSE" | grep -q '"code":409'; then
                    echo "PVC for $cluster_name already exists, skipping creation"
                    return 0
                # Check if PVC was created successfully
                elif echo "$RESPONSE" | grep -q '"kind": "PersistentVolumeClaim"'; then
                    echo "PVC created successfully for $cluster_name"
                    return 0
                else
                    echo "Failed to create PVC for $cluster_name. Response: $RESPONSE"
                    return 1
                fi
            }

            echo "$CLUSTER_CONFIG" | jq -c '.[]' | while read -r cluster; do
                NAME=$(echo "$cluster" | jq -r '.name')
                HOST=$(echo "$cluster" | jq -r '.mongo_host')
                PORT=$(echo "$cluster" | jq -r '.mongo_port')
                Password=$$(echo "$cluster" | jq -r '.mongo_password')
                Username=$$(echo "$cluster" | jq -r '.mongo_username')
                Auth_DB=$$(echo "$cluster" | jq -r '.auth_database')
                

                echo "Processing cluster: $NAME"

                DB_SIZE=$(mongo "mongodb://$HOST:$PORT" --username $Username --password $Password --authenticationDatabase $Auth_DB \
                    --quiet --eval "print((db.adminCommand({listDatabases: 1}).totalSize / (1024*1024*1024)).toFixed(0))")
                
                echo "DB_SIZE: $DB_SIZE"
                   
                if [ "$DB_SIZE" = "NaN" ]; then
                    echo "DB_SIZE is NaN. Retrying with rs.secondaryOk() and robust error handling..."
                    DB_SIZE=$(mongo "mongodb://$HOST:$PORT" \
                        --username $Username \
                        --password $Password \
                        --authenticationDatabase $Auth_DB \
                        --quiet \
                        --eval "rs.secondaryOk(); \
                                let result = db.adminCommand({listDatabases: 1}); \
                                let totalSizeBytes = 0; \
                                if (result && typeof result.totalSize === 'number') { \
                                    totalSizeBytes = result.totalSize; \
                                } \
                                let totalSizeGB = (totalSizeBytes / (1024 * 1024 * 1024)); \
                                print(totalSizeGB.toFixed(0));"
                    )
                    echo "Retried DB_SIZE: $DB_SIZE"
                fi

                   
                if [ -z "$DB_SIZE" ]; then
                    echo "Failed to fetch DB size for $NAME"
                    exit 1
                fi

                DB_SIZE=$(($DB_SIZE + 1))
                RESTORE_SIZE=$(($DB_SIZE + 1))
                
                echo "Creating PVC with size ${DB_SIZE}Gi"
                echo "Creating PVC: mongo-backup-pvc-$NAME with size ${DB_SIZE}Gi"
                create_pvc "mongo-backup-pvc-$NAME" "$DB_SIZE" || exit 1
                
                DAY_OF_WEEK=$(date +%u)  # Monday=1, Sunday=7

                echo "Today is weekday number: $DAY_OF_WEEK"
                if [ "$DAY_OF_WEEK" -eq 2 ]; then
                    echo "It's Monday, creating restore PVC with size ${RESTORE_SIZE}Gi"
                    echo "Creating PVC: mongo-restore-pvc-$NAME with size ${RESTORE_SIZE}Gi"
                    create_pvc "mongo-restore-pvc-$NAME" "$RESTORE_SIZE" || exit 1
                fi
            done
        '''],
    )

    # Create task groups for mongodump operations
    backup_groups = []  # Store all task groups
    for cluster in cluster_config['clusters']:
        with TaskGroup(group_id=f"backup_{cluster['name']}") as tg:

            # Task: Perform MongoDB Backup
            mongodump_task = KubernetesPodOperator(
                task_id=f"run_mongodump_{cluster['name']}",
                name=f"mongodump-{cluster['name']}",
                namespace="airflow",
                do_xcom_push=True,  # Enable XCom pushing
                image="mongo:4.4",
                node_selector={"nodepool": "staging-solvei8-amd-on-demand-cpu-intensive"},
                # --- Add tolerations here for Karpenter's startupTaints ---
                tolerations=[
                    k8s.V1Toleration(
                        key="workload",
                        operator="Equal",
                        value="ondemand-cpu",
                        effect="NoSchedule",
                    )
                ],
                container_resources=k8s.V1ResourceRequirements(
                    requests={"cpu": "1500m", "memory": "500Mi"},
                    limits={"cpu": "2000m", "memory": "800Mi"},
                ),
                volumes=[
                    k8s.V1Volume(
                        name="backup-storage",
                        persistent_volume_claim=k8s.V1PersistentVolumeClaimVolumeSource(
                            claim_name=f"mongo-backup-pvc-{cluster['name']}"
                        )
                    )
                ],
                volume_mounts=[
                    k8s.V1VolumeMount(
                        name="backup-storage",
                        mount_path="/backup"
                    )
                ],
                cmds=["sh", "-c"],
                arguments=[
                    f'''
                    TIMESTAMP="{{{{ ts_nodash }}}}"
                    BACKUP_DIR="/backup/mongo_backup_$TIMESTAMP"
                    rm -rf /backup/*
                    mkdir -p $BACKUP_DIR

                     mongodump --host={cluster["mongo_host"]} \
                              --port={cluster["mongo_port"]} --username {cluster["mongo_username"]} --password {cluster["mongo_password"]} --authenticationDatabase {cluster["auth_database"]} \
                              --out=$BACKUP_DIR \
                               --gzip

                    echo "Backup completed at $(date)" > "$BACKUP_DIR/backup_info.txt"

                    # Write the backup path to XCom in proper JSON format
                    echo "{{\\"backup_path\\": \\"$BACKUP_DIR\\"}}" > /airflow/xcom/return.json
                    '''
                ],
            )

            # Task: Upload to S3
            upload_to_s3_task = PythonOperator(
                task_id=f"upload_to_s3_{cluster['name']}",
                python_callable=upload_backup_to_s3,
                op_kwargs={
                    'cluster_name': cluster['name'],
                    's3_bucket': cluster['s3_bucket_name'],
                    's3_prefix': cluster['s3_backup_folder']
                },
                executor_config={
                    "pod_override": k8s.V1Pod(
                        spec=k8s.V1PodSpec(
                            containers=[
                                k8s.V1Container(
                                    name="base",
                                    volume_mounts=[
                                        k8s.V1VolumeMount(
                                            name="backup-storage",
                                            mount_path="/backup"
                                        )
                                    ],
                                    resources=k8s.V1ResourceRequirements(
                                        requests={"cpu": "200m", "memory": "400Mi"},
                                        limits={"cpu": "400m", "memory": "700Mi",},
                                    ),

                                )
                            ],
                            volumes=[
                                k8s.V1Volume(
                                    name="backup-storage",
                                    persistent_volume_claim=k8s.V1PersistentVolumeClaimVolumeSource(
                                        claim_name=f"mongo-backup-pvc-{cluster['name']}"
                                    )
                                )
                            ]
                        )
                    )
                }
            )

            # Task to branch based on day of the week
            branch_task = BranchPythonOperator(
                task_id=f"branch_on_day_{cluster['name']}",
                python_callable=branch_based_on_monday,
                provide_context=True,
                params={
                    'tg_prefix': f"backup_{cluster['name']}",
                    'cluster_name': cluster['name']
                },
            )
            # Dummy task for the "skip" path on non-Mondays.
            skip_restore_task = DummyOperator(
                task_id=f"dummy_monday_restore_skip_{cluster['name']}"
            )
            # Task: Check collections
            check_collections_task = KubernetesPodOperator(
                task_id=f"check_collections_{cluster['name']}",
                name=f"check-collections-{cluster['name']}",
                namespace="airflow",
                image="mongo:4.4",
                cmds=["sh", "-c"],
                arguments=[
                    f'''
                    echo "Checking collections for cluster: {cluster['name']}";
                    mongo "mongodb://{cluster['mongo_host']}:{cluster['mongo_port']}" \
                        --username {cluster['mongo_username']} \
                        --password {cluster['mongo_password']} \
                        --authenticationDatabase {cluster['auth_database']} \
                        --quiet \
                        --eval '
                            var result = {{}};
                            var grandTotal = 0;
                            var dbSizes = [];
                            db.getMongo().getDBNames().forEach(function(dbName) {{
                                if (dbName !== "local" && dbName !== "config" && dbName !== "admin") {{
                                    var dbTotal = 0;
                                    var dbObject = db.getSiblingDB(dbName);
                                    var collectionNames = dbObject.getCollectionNames();
                                    collectionNames.forEach(function(collectionName) {{
                                        var count = dbObject.getCollection(collectionName).count();
                                        dbTotal += count;
                                    }});
                                    grandTotal += dbTotal;
                                    dbSizes.push({{ database: dbName, totalDocuments: dbTotal }});
                                }}
                            }});
                            var totalSizeBytes = db.adminCommand({{listDatabases:1}}).totalSize;
                            var sizeGB = Math.ceil(totalSizeBytes / (1024*1024*1024));
                            result["db_sizes"] = dbSizes;
                            result["grand_total_documents"] = grandTotal;
                            result["cluster_size_gb"] = sizeGB;
                            printjson(result);
                        ' > output.json

                    cp output.json /airflow/xcom/return.json
                    '''
                ],
                do_xcom_push=True,
            )
            # # Task to restore MongoDB
            # Define the full task_id for xcom_pull
            mongodump_full_task_id = f"backup_{cluster['name']}.run_mongodump_{cluster['name']}"
            # Task to restore MongoDB
            mongo_restore_task = KubernetesPodOperator(
                task_id=f"restore_mongodb_{cluster['name']}",
                name=f"mongorestore-{cluster['name']}",
                namespace="airflow",
                image="mongo:4.4",
                do_xcom_push=True,
                node_selector={"nodepool": "staging-solvei8-amd-on-demand-cpu-intensive"},
                tolerations=[
                    k8s.V1Toleration(
                        key="workload",
                        operator="Equal",
                        value="ondemand-cpu",
                        effect="NoSchedule",
                    ),
                ],
                container_resources=k8s.V1ResourceRequirements(
                    requests={"cpu": "3000m", "memory": "2000Mi"},
                    limits={"cpu": "3200m", "memory": "2500Mi"},
                ),
                volumes=[
                    k8s.V1Volume(
                        name="backup-storage",
                        persistent_volume_claim=k8s.V1PersistentVolumeClaimVolumeSource(
                            claim_name=f"mongo-backup-pvc-{cluster['name']}"
                        )
                    ),
                    k8s.V1Volume(
                        name="restore-data-storage",
                        persistent_volume_claim=k8s.V1PersistentVolumeClaimVolumeSource(
                            claim_name=f"mongo-restore-pvc-{cluster['name']}"
                        )
                    )
                ],
                volume_mounts=[
                    k8s.V1VolumeMount(
                        name="backup-storage",
                        mount_path="/backup", # Mount the backup PVC here
                        read_only=True
                    ),
                    k8s.V1VolumeMount(
                        name="restore-data-storage",
                        mount_path="/data/db" # Mount the restore PVC here for MongoDB data
                    )
                ],
                env_vars={
                    "BACKUP_PATH": f"{{{{ ti.xcom_pull(task_ids='{mongodump_full_task_id}', key='return_value')['backup_path'] }}}}"
                },      
                cmds=["sh", "-c"],
                arguments=[f'''
                    mongod --bind_ip_all > /dev/null 2>&1 &
                    echo "Pinging MongoDB to verify connectivity..."
                    sleep 20
                    mongo --eval 'db.runCommand("ping")' > /dev/null 2>&1
                    if [ $? -eq 0 ]; then
                        echo "MongoDB connectivity verified ✅"
                    else
                        echo "Failed to connect to MongoDB ❌"
                        exit 1
                    fi
                    echo "MongoDB started ✅"
                    echo "Restoring backup from $BACKUP_PATH"
                    sleep 5400
                    
                    # mongorestore  --gzip --drop  $BACKUP_PATH 
                    # sleep 10
                    mongo --quiet --eval "db.adminCommand('listDatabases')"

                    echo "Checking collections for cluster: {cluster['name']}"

                    mongo --quiet \
                        --eval '
                            var result = {{}};
                            var grandTotal = 0;
                            var dbSizes = [];
                            db.getMongo().getDBNames().forEach(function(dbName) {{
                                if (dbName !== "local" && dbName !== "config" && dbName !== "admin") {{
                                    var dbTotal = 0;
                                    var dbObject = db.getSiblingDB(dbName);
                                    var collectionNames = dbObject.getCollectionNames();
                                    collectionNames.forEach(function(collectionName) {{
                                        var count = dbObject.getCollection(collectionName).count();
                                        dbTotal += count;
                                    }});
                                    grandTotal += dbTotal;
                                    dbSizes.push({{ database: dbName, totalDocuments: dbTotal }});
                                }}
                            }});
                            var totalSizeBytes = db.adminCommand({{listDatabases:1}}).totalSize;
                            var sizeGB = Math.ceil(totalSizeBytes / (1024*1024*1024));
                            result["db_sizes"] = dbSizes;
                            result["grand_total_documents"] = grandTotal;
                            result["cluster_size_gb"] = sizeGB;
                            printjson(result);
                        ' > output.json
                    cp output.json /airflow/xcom/return.json
                ''']
            )

            # --- New Task for verification ---
            verify_restore_counts_task = PythonOperator(
                task_id=f"verify_restore_counts_{cluster['name']}",
                python_callable=compare_restore_counts,
                op_kwargs={'cluster_name': cluster['name']},
            )
            
            # --- Define Dependencies ---
            # Daily backup and upload
            mongodump_task >> upload_to_s3_task

            # The branch is triggered after the daily upload.
            upload_to_s3_task >> branch_task

            # The branch task routes the flow.
            branch_task >> [check_collections_task, skip_restore_task]

            # The Monday-only restore path
            check_collections_task >> mongo_restore_task >> verify_restore_counts_task

            backup_groups.append(tg)


    # Task to delete all PVCs
    cleanup_pvcs = KubernetesPodOperator(
        task_id="cleanup_pvcs",
        name="cleanup-pvcs",
        namespace="airflow",
        service_account_name='airflow',
        image="bitnami/kubectl:latest",
        cmds=["sh", "-c"],
        env_vars={
        "CLUSTER_CONFIG": json.dumps(cluster_config['clusters'])  # Pass clusters as JSON
        },
        trigger_rule='all_done',
        arguments=['''
            service_account_path="/var/run/secrets/kubernetes.io/serviceaccount"
            namespace=$(cat $service_account_path/namespace)
            echo "$CLUSTER_CONFIG" | jq -r '.[].name' | while read cluster_name; do
                printf "Deleting PVC for cluster: %s\n" "$cluster_name"
                kubectl delete pvc "mongo-backup-pvc-$cluster_name" -n "$namespace" --ignore-not-found=true
                kubectl delete pvc "mongo-restore-pvc-$cluster_name" -n "$namespace" --ignore-not-found=true
            done
            '''
        ],
    )

    # Set dependencies
    create_all_pvcs >> backup_groups
    chain(backup_groups, cleanup_pvcs)
