import requests
import logging
import base64
import re
import os
import json
import tempfile
from datetime import datetime, timedelta, timezone
from requests.auth import HTT<PERSON><PERSON><PERSON>c<PERSON>uth
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.dates import days_ago
from utility.slack_alerts import on_failure_callback, on_retry_callback
from airflow.models import Variable

# Get the directory where this file is located
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))

dag_name = "api_usage_report_dag"
CONFIG = Variable.get("passphrase_" + dag_name + "_config", deserialize_json=True)
SECRET_CONFIG = Variable.get(dag_name, deserialize_json=True)
print(SECRET_CONFIG)

class GraylogClient:
    def __init__(self):
        self.url = CONFIG['graylog']['url'].rstrip('/')
        self.auth = HTTPBasicAuth(
            CONFIG['graylog']['username'],
            CONFIG['graylog']['password']
        )
        self.headers = {'X-Requested-By': 'graylog-test-client'}
        self.streams = self._fetch_streams()

    def _fetch_streams(self):
        try:
            response = requests.get(
                "{}/api/streams".format(self.url),
                auth=self.auth,
                headers=self.headers
            )
            response.raise_for_status()
            return response.json().get('streams', [])
        except Exception as e:
            logging.error("Failed to fetch Graylog streams: %s", str(e))
            return []

    def get_stream_id(self, stream_title):
        for stream in self.streams:
            if stream['title'] == stream_title:
                return stream['id']
        return None

    def get_api_usage(self, stream_id, route):
        try:
            end_time = datetime.now()

            query_body = {
                "queries": [{
                    "timerange": {
                        "type": "relative",
                        "range": SECRET_CONFIG.get('graylog', {}).get('days_range', 15) * 86400  # Convert days to seconds
                    },
                    "query": {
                        "type": "elasticsearch",
                        "query_string": "http_uri:{}".format(self._convert_route_to_pattern(route['path']))
                    },
                    "filter": {
                        "type": "stream",
                        "id": stream_id
                    },
                    "search_types": [{
                        "type": "pivot",
                        "name": "total_http_uri_count",
                        "rollup": True,
                        "series": [{
                            "type": "count"
                        }]
                    }]
                }]
            }
            
            # Dump query_body to file for debugging
            debug_file = f"/tmp/query_body_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(debug_file, 'w') as f:
                json.dump(query_body, f, indent=2)
            logging.info(f"Query body dumped to file: {debug_file}")
            print(f"Query body dumped to file: {debug_file}")
            with open(debug_file, 'r') as f:
                print(f.read())
            
            
            response = requests.post(
                "{}/api/views/search/sync".format(self.url),
                auth=self.auth,
                json=query_body,
                headers=self.headers
            )
            response.raise_for_status()

            data = response.json()
            result_id = list(data["results"].keys())[0]
            search_type_id = list(data["results"][result_id]["search_types"].keys())[0]
            count = data["results"][result_id]["search_types"][search_type_id]["rows"][0]["values"][0]["value"]
            print(f"Count: {count}")
            return int(count)

        except Exception as e:
            logging.error("Failed to fetch API usage for route {}: {}".format(route['path'], str(e)))
            return 0

    def _convert_route_to_pattern(self, route):
        pattern = re.sub(r':[^/]+', '*', route)
        pattern = pattern.replace('/', '\\/')
        return pattern

class ServiceRoutesFetcher(object):
    def __init__(self):
        self.consul_url = CONFIG['consul']['url'].rstrip('/')
        self.setup_logging()
        self.timeout = CONFIG['consul'].get('timeout', 10)
        self.routes_cache = {}
        self.graylog = GraylogClient()
        # Remove pygsheets initialization from here
        
    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)

    def get_git_url_from_consul(self, service_name):
        try:
            consul_key = "admin/jenkins/nfactory/backend/git/{}".format(service_name)
            self.logger.info("Fetching git URL from Consul for service: {}".format(service_name))

            response = requests.get(
                "{}/v1/kv/{}".format(self.consul_url, consul_key),
                timeout=self.timeout
            )

            if response.status_code != 200:
                raise Exception("Consul returned status code: {}".format(response.status_code))

            consul_data = response.json()[0]
            if not consul_data.get('Value'):
                raise Exception("No git URL found for service {}".format(service_name))

            git_url = base64.b64decode(consul_data['Value']).decode('utf-8')
            return git_url.strip()

        except Exception as e:
            self.logger.error("Failed to get git URL from Consul: {}".format(str(e)))
            raise

    def parse_git_url(self, git_url):
        try:
            if git_url.startswith('git@'):
                repo_path = git_url.replace('*****************:', '')
                repo_path = repo_path.replace('.git', '')
            elif git_url.startswith('https://'):
                repo_path = git_url.replace('https://bitbucket.org/', '')
                repo_path = repo_path.replace('.git', '')
            else:
                raise ValueError("Unsupported git URL format: {}".format(git_url))
            return repo_path

        except Exception as e:
            self.logger.error("Failed to parse git URL: {}".format(str(e)))
            raise

    def fetch_routes_file(self, repo_path):
        try:
            workspace = CONFIG['bitbucket']['workspace']
            repo_slug = repo_path.split('/')[-1]
            routes_path = "conf/routes"

            url = "https://api.bitbucket.org/2.0/repositories/{}/{}/src/master/{}".format(
                workspace, repo_slug, routes_path
            )

            self.logger.info("Fetching routes file from Bitbucket for repo: {}".format(repo_path))
            response = requests.get(
                url,
                auth=HTTPBasicAuth(
                    CONFIG['bitbucket']['username'],
                    CONFIG['bitbucket']['app_password']
                )
            )

            if response.status_code == 200:
                return response.text
            elif response.status_code == 404:
                raise Exception("Routes file not found for {}".format(repo_path))
            elif response.status_code == 403:
                raise Exception("Bitbucket authentication failed")
            else:
                raise Exception("Failed to fetch routes file from Bitbucket (status: {})".format(
                    response.status_code))

        except Exception as e:
            self.logger.error("Failed to fetch routes file: {}".format(str(e)))
            raise

    def parse_routes(self, content):
        routes = []
        for line in content.splitlines():
            line = line.strip()
            if line and not line.startswith('#'):
                parts = line.split()
                if len(parts) >= 2:
                    routes.append({
                        'method': parts[0],
                        'path': parts[1]
                    })
        return routes

    def get_service_routes(self, service_name):
        if service_name in self.routes_cache:
            return self.routes_cache[service_name]

        try:
            git_url = self.get_git_url_from_consul(service_name)
            repo_path = self.parse_git_url(git_url)
            routes_content = self.fetch_routes_file(repo_path)
            routes = self.parse_routes(routes_content)

            self.routes_cache[service_name] = routes
            return routes

        except Exception as e:
            self.logger.error("Failed to get routes for service {}: {}".format(service_name, str(e)))
            raise

    def get_api_usage(self, service_name, route):
        stream_title = "{}_{}_{}_{}".format(
            CONFIG['graylog']['env_type'],
            'nsg1',
            service_name,
            CONFIG['graylog']['streams_suffix']
        )
        stream_id = self.graylog.get_stream_id(stream_title)
        if not stream_id:
            self.logger.warning("Stream not found for service: %s", service_name)
            return 0

        return self.graylog.get_api_usage(stream_id, route)

    def create_new_sheet(self):
        import pygsheets
        
        timestamp = datetime.now().strftime("%d-%b-%Y_%I:%M-%p")
        sheet_name = "api_report_{}".format(timestamp)
        print(sheet_name)

        # get the service account json form the airflow variable
        service_account_json = Variable.get("authorization_api_usage_report_google_sheet_service_account")
            
        # Create a temporary file to store the JSON content
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
            temp_file_path = temp_file.name
            json.dump(json.loads(service_account_json), temp_file)
            
            # Initialize client with the temporary service account file
        client = pygsheets.authorize(service_account_file=temp_file_path)
        # Open the spreadsheet by its ID
        spreadsheet = client.open_by_key(CONFIG['google_sheet']['spreadsheet_id'])

        # Create a new worksheet with the dynamic name
        worksheet = spreadsheet.add_worksheet(sheet_name, rows=100, cols=4)

        # Add headers
        worksheet.update_values("A1:D1", [['Service', 'Method', 'Path', 'Usage Count']])

        return worksheet

def main():
    # First install required package
    import sys
    import subprocess
    subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pygsheets'])
    
    fetcher = ServiceRoutesFetcher()

    all_data = []  # List to accumulate data for all services
    services_config = Variable.get(dag_name, deserialize_json=True)
    for cluster, service_list in services_config['services'].items():
        for service_name in service_list:  # List your services here
            try:
                print(f"\nProcessing service: {service_name} in env {cluster}")
                routes = fetcher.get_service_routes(service_name)

                for route in routes:
                    usage_count = fetcher.get_api_usage(service_name, route)
                    all_data.append([
                        service_name,
                        route['method'],
                        route['path'],
                        usage_count
                    ])

            except Exception as e:
                print(f"Error processing service {service_name}: {str(e)}")
    # Create a new sheet after collecting all data
    if all_data:
        worksheet = fetcher.create_new_sheet()

        # Add headers first
        worksheet.update_values("A1:D1", [['Service', 'Method', 'Path', 'Usage Count']])

        # Now update all the rows in one go
        worksheet.update_values("A2:D{}".format(len(all_data) + 1), all_data)

    print("Report generated and added to Google Sheets.")

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'start_date': days_ago(1),
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
    'on_failure_callback': on_failure_callback,
    'on_retry_callback': on_retry_callback,
}

with DAG(
    dag_name,
    default_args=default_args,
    description='Generate API usage report',
    schedule_interval='0 0 1,16 * *',  # “At 00:00 on day-of-month 1 and 16.”
    catchup=False,
    tags=['api', 'report'],
) as dag:

    generate_report = PythonOperator(
        task_id='generate_api_usage_report',
        python_callable=main,
        provide_context=True,
        dag=dag,
    )
