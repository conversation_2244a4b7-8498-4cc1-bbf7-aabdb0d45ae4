from airflow import DAG
from airflow.providers.cncf.kubernetes.operators.kubernetes_pod import KubernetesPodOperator
from airflow.models import Variable
from datetime import datetime, timedelta
# If using k8s env vars for AWS creds
# from kubernetes.client import models as k8s

# --- Configuration ---
METABASE_DB_USER = "root"
METABASE_DB_PASSWORD = "" # Mark as sensitive in Airflow UI
MARIADB_HOST = "************"
MARIADB_PORT = 3306 # Ensure this is accessible from your K8s cluster
METABASE_DB_NAME = "metabase"
S3_BUCKET_NAME = "metabase-backup-solvei8"

with DAG(
    dag_id='metabase_mariadb_backup_k8s_combined_image',
    start_date=datetime(2023, 1, 1),
    schedule_interval='0 3 * * *',
    catchup=False,
    tags=['metabase', 'backup', 'mariadb', 's3', 'kubernetes'],
    default_args={
        'owner': 'airflow',
        'depends_on_past': False,
        'email_on_failure': False,
        'email_on_retry': False,
        'retries': 1,
        'retry_delay': timedelta(minutes=5),
    },
) as dag:
    backup_command_script = f"""
        #!/bin/bash
        set -euo pipefail # Exit on error, unset variables, pipefail

        BACKUP_FILE_NAME="metabase_$(date +"%Y-%m-%d").sql"
        LOCAL_TEMP_DIR="/tmp/metabase_backup" # Temp directory inside the K8s pod
        BACKUP_PATH="${{LOCAL_TEMP_DIR}}/${{BACKUP_FILE_NAME}}"

        mkdir -p ${{LOCAL_TEMP_DIR}}

        echo "--- Starting Metabase database backup from K8s pod ---"
        echo "Attempting to dump database '{METABASE_DB_NAME}' from {MARIADB_HOST}:{MARIADB_PORT} to ${{BACKUP_PATH}}"

        # 1. Perform the mysqldump
        # Note: password syntax for mysqldump is -pPASSWORD (no space)
        mysqldump -h {MARIADB_HOST} -P {MARIADB_PORT} -u {METABASE_DB_USER} -p'{METABASE_DB_PASSWORD}' {METABASE_DB_NAME} > "${{BACKUP_PATH}}"

        echo "Database backup successful: ${{BACKUP_PATH}}"
        echo "File size: $(du -sh ${{BACKUP_PATH}})"

        echo "--- Uploading backup to S3 ---"
        echo "Target S3 path: s3://{S3_BUCKET_NAME}/${{BACKUP_FILE_NAME}}"

        # 2. Upload to S3 (aws cli must be installed in BACKUP_DOCKER_IMAGE)
        aws s3 cp "${{BACKUP_PATH}}" s3://{S3_BUCKET_NAME}/${{BACKUP_FILE_NAME}}

        echo "Upload to S3 successful!"

        # 3. Clean up local backup file after successful upload
        echo "Cleaning up local backup file..."
        rm -f "${{BACKUP_PATH}}"
        echo "--- Metabase backup process completed ---"
    """

    backup_metabase_k8s = KubernetesPodOperator(
        task_id='dump_metabase_and_upload_to_s3_k8s',
        namespace="airflow",
        image="",
        cmds=["/bin/bash", "-cx"], # Execute the command script
        arguments=[backup_command_script],
        name="metabase-backup-pod",
        is_delete_operator_pod=True,
        get_logs=True,
        startup_timeout_seconds=300,
        # Or pass AWS credentials via environment variables from K8s Secrets if not using IRSA
        # env_vars=[
        #     k8s.V1EnvVar(name="AWS_ACCESS_KEY_ID", value_from=k8s.V1EnvVarSource(secret_key_ref=k8s.V1SecretKeySelector(name="my-aws-secret", key="aws_access_key_id"))),
        #     k8s.V1EnvVar(name="AWS_SECRET_ACCESS_KEY", value_from=k8s.V1EnvVarSource(secret_key_ref=k8s.V1SecretKeySelector(name="my-aws-secret", key="aws_secret_access_key"))),
        #     k8s.V1EnvVar(name="AWS_DEFAULT_REGION", value="your-aws-region"), # Or get from Variable
        # ],
    )