import os
import datetime
import subprocess

from airflow import DAG
from airflow.models import Variable
from airflow.exceptions import AirflowException
from airflow.operators.python import PythonOperator
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
# from utility.config_validator import get_validated_config

dag_name = "mariadb_backup_to_s3_dag"
# required_keys = ["host", "username", "password", "database", "s3_bucket"]
# cluster_required_keys = []

# config = get_validated_config(
#     dag_name=dag_name,
#     required_keys=required_keys,
#     cluster_required_keys=cluster_required_keys
# )

def backup_mariadb_to_s3_from_variable() -> str:
    # Load configuration from Airflow variable
    config = Variable.get(dag_name, deserialize_json=True)

    host = config["host"]
    port = config.get("port", 3306)
    user = config["username"]
    password = config["password"]
    database = config["database"]
    s3_bucket = config["s3_bucket"]
    s3_prefix = config["s3_prefix"]

    # File paths
    today = datetime.date.today().strftime("%Y-%m-%d")
    file_name = f"{database}_backup_{today}.sql"
    local_file = f"/tmp/{file_name}"
    s3_key = f"{s3_prefix}{file_name}"

    # Construct mariadb-dump command
    dump_command = f"mariadb-dump --host={host} --port={port} --user={user} --password={password} {database}"

    # Run the dump
    try:
        with open(local_file, "w") as f:
            subprocess.run(dump_command, shell=True, stdout=f, stderr=subprocess.PIPE, check=True)
    except FileNotFoundError:
        raise AirflowException("mariadb_dump not found in system path.")
    except subprocess.CalledProcessError as e:
        raise AirflowException(f"Database dump failed: {e.stderr.decode().strip()}")
    except Exception as e:
        raise AirflowException(f"Unexpected error: {e}")

    # Upload to S3
    try:
        s3 = S3Hook(aws_conn_id="solvei8-stateful-backups-connection")
        s3.load_file(filename=local_file, key=s3_key, bucket_name=s3_bucket, replace=True)
        return s3_key
    except Exception as e:
        raise AirflowException(f"S3 upload failed: {e}")
    finally:
        if os.path.exists(local_file):
            os.remove(local_file)

# Define the DAG
with DAG(
    dag_id=dag_name,
    start_date=datetime.datetime(2023, 1, 1),
    schedule_interval="@daily",
    catchup=False,
    tags=["backup", "mariadb", "s3"],
) as dag:

    backup_task = PythonOperator(
        task_id="mariadb_backup_to_s3",
        python_callable=backup_mariadb_to_s3_from_variable,
    )
