# MongoDB Backup and Restore DAG with S3 Integration

## Overview

This Airflow DAG automates full backups and restores of multiple MongoDB clusters with storage on S3, using Kubernetes Pods for task execution. It dynamically manages Persistent Volume Claims (PVCs) for storage, runs MongoDB backup and restore commands in Kubernetes pods, and verifies restore results.

### what is the aim of this dag?
The aim of this DAG is to automate the reliable, efficient, and scalable process of backing up multiple MongoDB clusters daily and restoring them weekly, while securely storing backups in S3. It dynamically manages storage allocation with Kubernetes PVCs, runs backup and restore tasks inside Kubernetes pods for isolation and scalability, and verifies that the restore was successful by comparing document counts. This workflow optimizes resource usage by running full restore and verification only once a week (on Mondays) while ensuring daily backup completeness and offsite durability via S3 uploads.

## Key Components

- **Cluster Configurations:** Multiple MongoDB clusters are defined via validated configuration, including connection details and S3 bucket/folder info.

- **PVC Management:**  
  - Before backups, PVCs for each cluster are created dynamically based on DB size estimates.  
  - After the process, PVCs are cleaned up.

- **Backup and Restore Tasks (Per Cluster):**  
  Within a TaskGroup for each cluster, the tasks include:  
  1. **Branching on Execution Day:**  
     Uses a BranchPythonOperator to determine if it's Monday or not.  
     - On **Monday**, full workflow runs: backup → upload to S3 → check collections → restore → verify.  
     - On **other days**, only backup and upload happen.  

  2. **MongoDB Backup:**  
     Runs `mongodump` inside a Kubernetes pod, output stored on PVC.

  3. **S3 Upload:**  
     Upload backup files from PVC to configured S3 buckets.

  4. **Check Collections, Restore, and Verify:**  
     On Mondays, runs MongoDB restore and counts verification in Kubernetes pods and Python tasks.

- **Cleanup:**  
  Cleans all PVCs created during the run to free storage.

---

## Why This Design?

- **KubernetesPodOperator:**  
  Ensures Kubernetes-native containerized execution, resource isolation, and scalability.

- **Branching Based on Day:**  
  Optimizes resource usage by running restore and verification only once a week on Monday, while backups run daily.

- **Dynamic PVC Creation:**  
  Allocates storage precisely based on DB size, avoiding over-provisioning.

- **TaskGroup Usage:**  
  Organizes cluster-specific tasks neatly, improving DAG readability and manageability.

- **XComs for Data Passing:**  
  Mongo dump paths and verification reports are passed between tasks using Airflow's XCom mechanism.

---

## How To Use

- Ensure cluster configurations are correctly set and validated.
- Deploy the DAG to your Airflow environment with Kubernetes executor.
- DAG runs daily at 21:30 with conditional branching to handle restore workload on Mondays.
- Monitor task logs and XCom outputs for success/failure and verification reports.

---

This DAG provides a scalable, maintainable, and optimized solution for MongoDB backups and restores with integrated S3 storage.

