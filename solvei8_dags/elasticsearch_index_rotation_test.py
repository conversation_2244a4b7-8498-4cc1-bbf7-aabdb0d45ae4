from airflow import DAG
from airflow.operators.python import Python<PERSON>perator
from datetime import datetime, timedelta
from airflow.utils.dates import days_ago
from airflow.utils.task_group import TaskGroup
import requests
import re
from dateutil.parser import parse
from utility.slack_alerts import on_failure_callback, on_retry_callback
from utility.config_validator import get_validated_config

# Configuration validation
dag_name = "elasticsearch_index_rotation"
cluster_required_keys = ['name', 'es_host', 'es_port', 'delete_after_days', 'close_after_days', 'batch_size', 'index_match_patterns']

config = get_validated_config(
    dag_name=dag_name,
    required_keys=[],
    cluster_required_keys=cluster_required_keys
)

# ─── Airflow DAG Setup ──────────────────────────────────────────
default_args = {
    'owner': 'airflow',
    'retries': 1,
    'retry_delay': timedelta(minutes=1),
    'on_failure_callback': on_failure_callback,
    'on_retry_callback': on_retry_callback,
}

dag = DAG(
    dag_name,
    default_args=default_args,
    start_date=days_ago(1),
    schedule_interval='0 20 * * *',
    catchup=False,
    description='Close and delete indices across multiple Elasticsearch clusters'
)

# ─── Helpers using requests ─────────────────────────────────────
def check_es_health(es_host, es_port):
    try:
        response = requests.get(f"{es_host}:{es_port}/_cluster/health", timeout=30)
        response.raise_for_status()
        print(f"Elasticsearch health is OK for {es_host}")
    except Exception as e:
        raise RuntimeError(f"ES health check failed for {es_host}: {e}")

def get_all_indices(es_host, es_port):
    try:
        response = requests.get(f"{es_host}:{es_port}/_cat/indices?format=json", timeout=30)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        raise RuntimeError(f"Failed to fetch indices from {es_host}: {e}")

def delete_indices(es_host, es_port, indices):
    if not indices:
        return
    try:
        index_str = ",".join(indices)
        res = requests.delete(f"{es_host}:{es_port}/{index_str}", timeout=60)
        if res.status_code not in [200, 404]:
            raise RuntimeError(res.text)
        print(f"Deleted indices: {index_str} from {es_host}")

    except Exception as e:
        print(f"Error deleting indices {indices} from {es_host}: {e}")

def close_indices(es_host, es_port, indices):
    if not indices:
        return
    try:
        index_str = ",".join(indices)
        res = requests.post(f"{es_host}:{es_port}/{index_str}/_close", timeout=30)
        if res.status_code != 200:
            raise RuntimeError(res.text)
        print(f"Closed indices: {index_str} on {es_host}")
    except Exception as e:
        print(f"Error closing indices {indices} on {es_host}: {e}")

def list_read_only_indices(es_host, es_port):
    # todo just call the below url dont call call the index
    # curl localhost:9200/_all/_settings/index.blocks.read_only_allow_delete | jq '.'
    url = f"{es_host}:{es_port}/_all/_settings"
    try:
        response = requests.get(url, timeout=60)
        response.raise_for_status()
        settings = response.json()

        read_only_indices = []
        for index_name, index_settings in settings.items():
            blocks = index_settings.get("settings", {}).get("index", {}).get("blocks", {})
            if blocks.get("read_only_allow_delete") == "true":
                read_only_indices.append(index_name)
        print(read_only_indices)
        return read_only_indices

    except requests.RequestException as e:
        print(f"Error fetching index settings: {e}")
        return []

def remove_read_only(es_host, es_port, index_list):
    if not index_list:
        print("No indices to update.")
        return

    indices = ",".join(index_list)
    try:
        url = f"{es_host}:{es_port}/{indices}/_settings"
        payload = {
            "index": {
                "blocks": {
                    "read_only_allow_delete": False
                }
            }
        }
        res = requests.put(url, json=payload, timeout=30)
        if res.status_code != 200:
            raise RuntimeError(res.text)
        print(f"Removed read-only from: {indices} on {es_host}")
    except Exception as e:
        print(f"Error removing read-only from {indices} on {es_host}: {e}")

def count_indices_status(index_data):
    total = len(index_data)
    open_count = sum(1 for idx in index_data if idx.get("status") == "open")
    closed_count = sum(1 for idx in index_data if idx.get("status") == "close")
    return total, open_count, closed_count

# ─── Main Task: Manage Indices for a Cluster ────────────────────
def manage_cluster_indices(cluster_config):
    es_host = cluster_config['es_host']
    es_port = cluster_config['es_port']
    delete_after_days = cluster_config['delete_after_days']
    close_after_days = cluster_config['close_after_days']
    batch_size = cluster_config['batch_size']
    index_match_patterns = cluster_config['index_match_patterns']  # Will always be a list
    cluster_name = cluster_config['name']

    print(f"Processing cluster: {cluster_name} for prefixes: {index_match_patterns}")
    check_es_health(es_host, es_port)

    today = datetime.utcnow()
    index_data = get_all_indices(es_host, es_port)

    # Count before rotation
    total_before, open_before, closed_before = count_indices_status(index_data)
    print(f"[BEFORE] Total: {total_before}, Open: {open_before}, Closed: {closed_before}")

    # Remove read-only before processing indices
    index_list = list_read_only_indices(es_host, es_port)
    remove_read_only(es_host, es_port, index_list)

    print(f"Removed read-only from: {index_list} on {es_host}")
    to_delete_list = []
    to_close_list = []
    for idx in index_data:
        index_name = idx.get("index")
        if not index_name:
            continue

        # Check if index matches any of the prefixes
        if not any(substring in index_name for substring in index_match_patterns):
            continue

        match = re.search(r'\d{4}-\d{2}-\d{2}', index_name)
        if not match:
            print(f"No date found in index name: {index_name}")
            continue

        try:
            index_date = parse(match.group())
            days_old = (today - index_date).days
        except Exception as e:
            print(f"Error parsing date in {index_name}: {e}")
            continue

        if days_old > delete_after_days:
            print(f"Deleting index {index_name}, age {days_old} days")
            to_delete_list.append(index_name)
            if len(to_delete_list) >= batch_size:
                delete_indices(es_host, es_port, to_delete_list)
                to_delete_list = []
        elif days_old > close_after_days:
            # todo: close only those index which are open, dont close if it is already closed
            print(f"Closing index {index_name}, age {days_old} days")
            to_close_list.append(index_name)
            if len(to_close_list) >= batch_size:
                close_indices(es_host, es_port, to_close_list)
                to_close_list = []
        else:
            print(f"Skipping index {index_name}, age {days_old} days")

    # Final cleanup batch
    if to_delete_list:
        delete_indices(es_host, es_port, to_delete_list)

    if to_close_list:
        close_indices(es_host, es_port, to_close_list)

    # After rotation, fetch indices again
    index_data_after = get_all_indices(es_host, es_port)
    total_after, open_after, closed_after = count_indices_status(index_data_after)
    print(f"[AFTER] Total: {total_after}, Open: {open_after}, Closed: {closed_after}")

# ─── Create Task Groups for Each Cluster ───────────────────────
with dag:
    cluster_groups = []
    for cluster in config['clusters']:
        with TaskGroup(group_id=f"manage_indices_{cluster['name']}") as tg:
            PythonOperator(
                task_id=f"process_{cluster['name']}_indices",
                python_callable=manage_cluster_indices,
                op_kwargs={'cluster_config': cluster},
                dag=dag
            )
        cluster_groups.append(tg)

# Update DAG documentation
dag.doc_md = """
# Multi-Cluster Elasticsearch Index Rotation DAG

## Overview
This DAG manages the lifecycle of Elasticsearch indices across multiple clusters. For each cluster, it performs automatic rotation by closing old indices and deleting outdated ones based on configured retention periods.

## Configuration
The DAG uses an Airflow variable named `{{ dag_name }}` with the following structure:
```json
{
    "clusters": [
        {
            "name": "prod",
            "es_host": "http://elasticsearch1.prod.example.com",
            "es_port": "80",
            "delete_after_days": 7,
            "close_after_days": 3,
            "batch_size": 50,
            "index_match_patterns": ["myapp-ops-", "myapp-logs-"]
        },
        {
            "name": "staging",
            "es_host": "http://elasticsearch1.staging.example.com",
            "es_port": "80",
            "delete_after_days": 3,
            "close_after_days": 1,
            "batch_size": 50,
            "index_match_patterns": ["staging-ops-", "staging-logs-"]
        }
    ]
}
```

### Configuration Parameters
For each cluster:
- **name**: Cluster identifier
- **es_host**: Elasticsearch host URL
- **es_port**: Elasticsearch port number
- **delete_after_days**: Number of days before indices are deleted
- **close_after_days**: Number of days before indices are closed
- **batch_size**: Maximum number of indices to process in a single batch
- **index_match_patterns**: List of index prefixes to manage (e.g., ["myapp-ops-", "myapp-logs-"])

## Operations
For each configured cluster, the DAG:
1. Performs health check
2. Retrieves and processes indices matching any of the configured prefixes
3. Closes old indices and deletes outdated ones
4. Processes in configurable batches

## Schedule
- Runs daily (@daily)
- No catchup enabled
- Retries once with 1-minute delay

## Error Handling
- Includes failure and retry callbacks to Slack
- Validates configuration on startup
- Handles read-only indices by removing the read-only flag before operations

## Dependencies
- Requires network access to Elasticsearch cluster
- Needs proper Airflow variable configuration
- Depends on Python requests library

## Monitoring
- Logs all operations for tracking
- Reports success/failure via Slack callbacks
- Prints detailed information about skipped, closed, and deleted indices

## Notes
- Ensure proper backup before enabling this DAG
- Test with a small subset of indices first
- Monitor Elasticsearch performance during initial runs
"""
