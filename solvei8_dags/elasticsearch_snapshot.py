from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.models import Variable
from airflow.utils.dates import days_ago
from airflow.utils.task_group import TaskGroup
from airflow.sensors.python import PythonSensor
from kubernetes.client import models as k8s
from airflow.exceptions import AirflowException
from datetime import datetime, timedelta
import requests
import time
import json
from utility.slack_alerts import on_failure_callback, on_retry_callback
from utility.config_validator import get_validated_config

# -----------------------------------------------------------------------------
# Configuration
# -----------------------------------------------------------------------------
dag_name = "elasticsearch_snapshot_dag"
cluster_required_keys = ['name', 'es_host', 'es_port', 'es_s3_repo_name', 'es_s3_repo_bucket', 'es_s3_repo_bucket_region', 'retention_days']

config = get_validated_config(
    dag_name=dag_name,
    required_keys=[],
    cluster_required_keys=cluster_required_keys
)

# -----------------------------------------------------------------------------
# Helper Functions
# -----------------------------------------------------------------------------

def create_snapshot(cluster_config):
    """
    Checks prerequisites and creates a snapshot.
    - Verifies plugin
    - Checks or registers S3 repo
    - Verifies cluster health
    - Initiates snapshot
    """
    ELASTICSEARCH_HOST = cluster_config['es_host']
    ELASTICSEARCH_PORT = cluster_config['es_port']
    S3_REPO = cluster_config['es_s3_repo_name']
    CLUSTER_NAME = cluster_config['name']
    SNAPSHOT_NAME = f"es-snap-{cluster_config['name']}-{datetime.now().strftime('%Y-%m-%d-%H-%M-%S')}"

    try:
        # ─── Check if snapshot plugin is installed ───────────────────────────
        plugin_url = f"{ELASTICSEARCH_HOST}:{ELASTICSEARCH_PORT}/_cat/plugins?h=component"
        plugin_response = requests.get(plugin_url, timeout=30)

        if plugin_response.status_code != 200 or "repository-s3" not in plugin_response.text:
            raise Exception("Snapshot plugin 'repository-s3' is not installed or plugin check failed")

        print(f"✅ Cluster {cluster_config['name']}: Verified: Snapshot plugin is installed.")

        # ─── Check if S3 repository is registered ────────────────────────────
        repo_url = f"{ELASTICSEARCH_HOST}:{ELASTICSEARCH_PORT}/_snapshot/{S3_REPO}"
        repo_response = requests.get(repo_url, timeout=30)

        if repo_response.status_code != 200:
            print(f"⚠️  Cluster {cluster_config['name']}: Repository '{S3_REPO}' not found. Attempting to register...")

            register_payload = {
                "type": "s3",
                "settings": {
                    "client": "default",
                    "bucket": f"{cluster_config['es_s3_repo_bucket']}",
                    "region": f"{cluster_config['es_s3_repo_bucket_region']}",
                    "base_path": f"elasticsearch-backups/{cluster_config['name']}"
                }
            }

            try:
                register_response = requests.put(
                    repo_url,
                    headers={"Content-Type": "application/json"},
                    json=register_payload,
                    timeout=30
                )

                if register_response.status_code not in [200, 201]:
                    raise Exception(f"Failed to register S3 repo '{S3_REPO}': {register_response.text}")

                print(f"✅ Cluster {cluster_config['name']}: Successfully registered snapshot repository '{S3_REPO}'")

            except Exception as e:
                print(f"❌ Cluster {cluster_config['name']}: Error registering snapshot repo: {str(e)}")
                raise

        else:
            print(f"✅ Cluster {cluster_config['name']}: Verified: S3 snapshot repository '{S3_REPO}' is registered.")

        # ─── Check cluster health ─────────────────────────────────────────────
        health_url = f"{ELASTICSEARCH_HOST}:{ELASTICSEARCH_PORT}/_cluster/health"
        health_response = requests.get(health_url, timeout=30)
        cluster_status = health_response.json().get("status")

        if cluster_status not in ["green", "yellow"]:
            raise Exception(f"Cluster health is {cluster_status}. Aborting snapshot.")

        print(f"✅ Cluster {cluster_config['name']}: Cluster health is {cluster_status}. Proceeding with snapshot.")

        # ─── Create snapshot ─────────────────────────────────────────────────
        snapshot_url = f"{ELASTICSEARCH_HOST}:{ELASTICSEARCH_PORT}/_snapshot/{S3_REPO}/{SNAPSHOT_NAME}"
        payload = {
            "indices": "*",
            "ignore_unavailable": True,
            "include_global_state": True
        }

        snapshot_response = requests.put(
            snapshot_url,
            headers={"Content-Type": "application/json"},
            json=payload
        )

        if snapshot_response.status_code not in [200, 202]:
            raise Exception(f"Failed to initiate snapshot: {snapshot_response.text}")

        print(f"✅ Cluster {cluster_config['name']}: Snapshot '{SNAPSHOT_NAME}' creation initiated successfully.")
        return SNAPSHOT_NAME

    except Exception as e:
        print(f"❌ Cluster {cluster_config['name']}: Snapshot process failed: {str(e)}")
        raise

def check_snapshot_status(**kwargs):
    cluster_config = kwargs['cluster_config']
    ti = kwargs['ti']  # TaskInstance to pull XCom
    snapshot_name = ti.xcom_pull(task_ids=f"manage_snapshots_{cluster_config['name']}.create_snapshot")

    ELASTICSEARCH_HOST = cluster_config['es_host']
    ELASTICSEARCH_PORT = cluster_config['es_port']
    S3_REPO = cluster_config['es_s3_repo_name']
    url = f"{ELASTICSEARCH_HOST}:{ELASTICSEARCH_PORT}/_snapshot/{S3_REPO}/{snapshot_name}"

    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        data = response.json()

        snapshots = data.get('snapshots', [])
        if not snapshots:
            raise AirflowException(f"Snapshot {snapshot_name} not found!")

        snapshot_state = snapshots[0]['state']
        print(f"Cluster {cluster_config['name']}: Snapshot '{snapshot_name}' state: {snapshot_state}")

        if snapshot_state == "SUCCESS":
            return True
        elif snapshot_state == "IN_PROGRESS":
            return False
        else:
            raise AirflowException(f"Snapshot {snapshot_name} is in unexpected state: {snapshot_state}")

    except Exception as e:
        raise AirflowException(f"Failed to check snapshot status: {str(e)}")



def wait_for_healthy_cluster(es_host, es_port, max_retries=10, sleep_time=120):
    url = f"{es_host}:{es_port}/_cluster/health"
    for i in range(max_retries):
        try:
            res = requests.get(url, timeout=30)
            if res.ok:
                status = res.json().get("status")
                if status in ["green", "yellow"]:
                    print(f"Cluster healthy ({status})")
                    return True
                else:
                    print(f"[Attempt {i+1}] Cluster status: {status} — sleeping {sleep_time}s")
            else:
                print(f"[Attempt {i+1}] Failed to fetch health: {res.status_code}")
        except Exception as e:
            print(f"[Attempt {i+1}] Exception: {e}")
        time.sleep(sleep_time)
    raise Exception("Elasticsearch cluster is not healthy after retries")


def cleanup_old_snapshots(cluster_config):
    """
    Deletes snapshots older than the retention period.
    Retention period is defined in RETENTION_DAYS configuration.
    """
    ELASTICSEARCH_HOST = cluster_config['es_host']
    ELASTICSEARCH_PORT = cluster_config['es_port']
    S3_REPO = cluster_config['es_s3_repo_name']
    RETENTION_DAYS = int(cluster_config['retention_days'])

    url = f"{ELASTICSEARCH_HOST}:{ELASTICSEARCH_PORT}/_snapshot/{S3_REPO}/_all"
    response = requests.get(url, timeout=30)
    snapshots = response.json().get("snapshots", [])
    print(snapshots)

    retention_timestamp = (datetime.utcnow() - timedelta(days=RETENTION_DAYS)).timestamp()

    for snapshot in snapshots:
        if snapshot.get("end_time_in_millis", 0) / 1000 < retention_timestamp:
            snapshot_name = snapshot["snapshot"]
            print(snapshot_name)
            delete_url = f"{ELASTICSEARCH_HOST}:{ELASTICSEARCH_PORT}/_snapshot/{S3_REPO}/{snapshot_name}"
            # wait for cluster to be healthy
            wait_for_healthy_cluster(ELASTICSEARCH_HOST, ELASTICSEARCH_PORT)
            # delete snapshot
            del_response = requests.delete(delete_url, timeout=1200)
            
            if del_response.status_code == 200:
                print(f"Cluster {cluster_config['name']}: Deleted old snapshot: {snapshot_name}")
            else:
                print(f"Cluster {cluster_config['name']}: Failed to delete snapshot {snapshot_name}: {del_response.text}")

# -----------------------------------------------------------------------------
# DAG Definition
# -----------------------------------------------------------------------------
default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": days_ago(1),
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": on_failure_callback,
    "on_retry_callback": on_retry_callback,
}

dag = DAG(
    dag_name,
    default_args=default_args,
    schedule_interval="30 20 * * *",
    catchup=False,
    description="Creates and manages Elasticsearch snapshots with retention policy across multiple clusters",
    tags=['elasticsearch', 'backup']
)

# Create task groups for each cluster
with dag:
    cluster_groups = []
    for cluster in config['clusters']:
        with TaskGroup(group_id=f"manage_snapshots_{cluster['name']}") as tg:
            create_snapshot_task = PythonOperator(
                task_id=f"create_snapshot",
                python_callable=create_snapshot,
                op_kwargs={'cluster_config': cluster},
                provide_context=True, 
            )
            wait_for_snapshot_task = PythonSensor(
                task_id="wait_for_snapshot",
                python_callable=check_snapshot_status,
                op_kwargs={'cluster_config': cluster},
                poke_interval=180,        # check every 3 min
                timeout=3600,            # maximum 60 min
                mode='reschedule',       # RESCHEDULE mode (not poke mode)
                executor_config={
                    "pod_override": k8s.V1Pod(
                        spec=k8s.V1PodSpec(
                            containers=[
                                k8s.V1Container(
                                    name="wait-for-snapshot-container", # <--- TARGET THE BASE CONTAINER
                                    resources=k8s.V1ResourceRequirements(
                                        requests={"cpu": "100m", "memory": "200Mi"},
                                        limits={"cpu": "200m", "memory": "400Mi"},
                                    ),
                                )
                            ]
                        )
                    )
                } 
            )
            cleanup_snapshots_task = PythonOperator(
                task_id=f"cleanup_snapshots",
                python_callable=cleanup_old_snapshots,
                op_kwargs={'cluster_config': cluster},
                executor_config={
                    "pod_override": k8s.V1Pod(
                         metadata=k8s.V1ObjectMeta( # <--- ADD METADATA HERE FOR LABELS
                            labels={
                                # Use these labels for monitoring in Grafana
                                "airflow_dag_id": dag.dag_id,
                                "airflow_task_id": "cleanup_snapshots",
                                "job_type": "snapshot-cleanup",
                                "environment": "staging"
                            }
                        ),
                        spec=k8s.V1PodSpec(
                            # node_selector={"nodepool": "staging-solvei8-amd-on-demand-cpu-intensive"},
                            # tolerations=[
                            #     k8s.V1Toleration(
                            #         key="workload",
                            #         operator="Equal",
                            #         value="ondemand-cpu",
                            #         effect="NoSchedule",
                            #     )
                            # ],  
                            containers=[
                                k8s.V1Container(
                                    name="es-cleanup-snapshots-container",
                                    # resources=k8s.V1ResourceRequirements(
                                    #     requests={"cpu": "100m", "memory": "200Mi"},
                                    #     limits={"cpu": "200m", "memory": "400Mi"},
                                    # ),
                                )
                            ]
                        )
                    )
                }
            )

            # Set dependencies within the task group
            create_snapshot_task >> wait_for_snapshot_task >>cleanup_snapshots_task

        cluster_groups.append(tg)

# Update DAG documentation
dag.doc_md = """
# Multi-Cluster Elasticsearch Snapshot DAG

## Overview
This DAG automates the process of creating and managing Elasticsearch snapshots across multiple clusters, ensuring data backup and retention compliance.

## Configuration
The DAG uses an Airflow variable named `elasticsearch_snapshot_dag` with the following structure:
```json
{
    "clusters": [
        {
            "name": "prod",
            "es_host": "http://elasticsearch1.prod.example.com",
            "es_port": "80",
            "es_s3_repo": "test-s3",
            "retention_days": "7"
        },
        {
            "name": "staging",
            "es_host": "http://elasticsearch1.staging.example.com",
            "es_port": "80",
            "es_s3_repo": "test-s3",
            "retention_days": "3"
        }
    ]
}
```

## Operations
For each cluster, the DAG:
1. **Cluster Health Check**
   * Verifies Elasticsearch cluster status
   * Proceeds only if status is 'green' or 'yellow'
   * Fails if cluster status is 'red'

2. **Snapshot Creation**
   * Creates a full snapshot of all indices
   * Includes global cluster state
   * Uses S3 repository for storage
   * Snapshot naming format: `es-snap-{cluster_name}-YYYY-MM-DD-HH-MM-SS`

3. **Retention Management**
   * Removes snapshots older than configured retention period
   * Retention period: Configurable per cluster
   * Maintains storage efficiency

## Schedule
* Runs daily (@daily)
* No catchup enabled
* Retries once with 5-minute delay
* Monitoring: Integrated with Slack alerts
"""
