from airflow.models import Variable
from typing import List, Dict, Optional

def get_validated_config(
    dag_name: str,
    required_keys: List[str],
    cluster_required_keys: Optional[List[str]] = None,
) -> dict:
    """
    Generic configuration validator for DAGs.
    
    Args:
        dag_name: Name of the DAG/variable to fetch config from
        required_keys: List of required top-level keys
        cluster_required_keys: List of required keys for each cluster (if using cluster config)
        additional_validations: Dictionary of additional validation functions
            Format: {'validation_name': (validation_function, error_message)}
    
    Returns:
        dict: Validated configuration
    
    Raises:
        KeyError: If required keys are missing
        ValueError: If validation fails
    """

    try:
        config = Variable.get(dag_name, deserialize_json=True)
    except KeyError:
        raise KeyError(f"Airflow Variable '{dag_name}' not found")
    except ValueError:
        raise ValueError(f"Airflow Variable '{dag_name}' is not valid JSON")
    
    # Validate top-level keys if provided
    if required_keys:
        missing_keys = [key for key in required_keys if key not in config]
        if missing_keys:
            raise KeyError(f"Missing required keys in {dag_name}: {', '.join(missing_keys)}")
    
    # Validate cluster configurations if required
    if cluster_required_keys:
        if 'clusters' not in config or not config['clusters']:
            raise KeyError(f"Missing or empty 'clusters' configuration in {dag_name}")
        
        for cluster in config['clusters']:
            # Check for missing keys
            missing_keys = [key for key in cluster_required_keys if key not in cluster]
            if missing_keys:
                raise KeyError(f"Missing required keys {missing_keys} for cluster {cluster.get('name', 'unnamed')} in {dag_name}")
            
            # Check for empty values
            empty_keys = [key for key in cluster_required_keys if not str(cluster[key]).strip()]
            if empty_keys:
                raise ValueError(f"Empty values found for keys {empty_keys} in cluster {cluster['name']}")
    
    return config

