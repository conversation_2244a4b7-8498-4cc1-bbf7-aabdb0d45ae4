apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: airflow
  name: airflow-pvc-role
rules:
- apiGroups: [""]
  resources: ["persistentvolumeclaims"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: [""]
  resources: ["persistentvolumes"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: airflow-pvc-rolebinding
  namespace: airflow
subjects:
- kind: ServiceAccount
  name: airflow
  namespace: airflow
roleRef:
  kind: Role
  name: airflow-pvc-role
  apiGroup: rbac.authorization.k8s.io