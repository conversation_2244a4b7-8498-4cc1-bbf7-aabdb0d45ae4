"""DAG to backup Consul K<PERSON> store to S3 daily."""

from datetime import datetime, timedelta, timezone
import json
import logging
import requests
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.utils.dates import days_ago
from airflow.utils.task_group import TaskGroup
from utility.slack_alerts import on_failure_callback, on_retry_callback
from utility.config_validator import get_validated_config

# Configuration validation
dag_name = "consul_kv_backup_to_s3"
cluster_required_keys = ["name", "HOST", "PORT", "S3_BUCKET", "S3_KEY_PREFIX"]

config = get_validated_config(
    dag_name=dag_name,
    required_keys=[],
    cluster_required_keys=cluster_required_keys
)

def backup_kv_raw_and_upload(cluster_config, **context):
    """Backup Consul KV store to S3."""
    try:
        # Get data from Consul
        url = f"http://{cluster_config['HOST']}:{cluster_config['PORT']}/v1/kv/?recurse=true"
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        kv_data = response.json()
        logging.info(f"Retrieved {len(kv_data)} KV pairs from Consul cluster {cluster_config['name']}")

        # Save to local file
        timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%d_%H-%M-%S")
        local_path = f"/tmp/consul-kv-backup-{cluster_config['name']}-{timestamp}.json"
        with open(local_path, "w") as f:
            json.dump(kv_data, f, indent=2)

        # Upload to S3
        s3 = S3Hook(aws_conn_id='solvei8-stateful-backups-connection')
        s3_key = f"{cluster_config['S3_KEY_PREFIX']}{cluster_config['name']}/consul-kv-backup-{timestamp}.json"
        try:
            s3.load_file(
                filename=local_path,
                key=s3_key,
                bucket_name=cluster_config['S3_BUCKET'],
                replace=True
            )
            logging.info(f"Backup uploaded to S3: {s3_key}")
        except Exception as e:
            logging.error(f"Failed to upload backup to S3: {str(e)}")
            raise
    except Exception as e:
        logging.error(f"Backup failed for cluster {cluster_config['name']}: {str(e)}")
        raise

default_args = {
    'owner': 'airflow',
    'retries': 1,
    'retry_delay': timedelta(minutes=1),
    'on_failure_callback': on_failure_callback,
    'on_retry_callback': on_retry_callback,
    'start_date': days_ago(1)
}

with DAG(
    dag_id=dag_name,
    schedule_interval="@daily",
    catchup=False,
    default_args=default_args,
    tags=['consul', 'backup'],
    description="Daily Consul KV backup to S3"
) as dag:

    backup_groups = []

    # Create a task group for each cluster
    for cluster in config['clusters']:
        with TaskGroup(group_id=f"backup_consul_{cluster['name']}") as tg:
            backup_task = PythonOperator(
                task_id=f"backup_and_upload",
                python_callable=backup_kv_raw_and_upload,
                op_kwargs={'cluster_config': cluster}
            )
            
        backup_groups.append(tg)
