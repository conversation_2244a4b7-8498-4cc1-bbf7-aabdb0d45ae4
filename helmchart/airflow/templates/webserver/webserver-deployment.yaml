{{- $podNodeSelector := include "airflow.podNodeSelector" (dict "Release" .Release "Values" .Values "nodeSelector" .Values.web.nodeSelector) }}
{{- $podTopologySpreadConstraints := include "airflow.podTopologySpreadConstraints" (dict "Release" .Release "Values" .Values "topologySpreadConstraints" .Values.web.topologySpreadConstraints) }}
{{- $podAffinity := include "airflow.podAffinity" (dict "Release" .Release "Values" .Values "affinity" .Values.web.affinity) }}
{{- $podTolerations := include "airflow.podTolerations" (dict "Release" .Release "Values" .Values "tolerations" .Values.web.tolerations) }}
{{- $podSecurityContext := include "airflow.podSecurityContext" (dict "Release" .Release "Values" .Values "securityContext" .Values.web.securityContext) }}
{{- $extraPipPackages := concat .Values.airflow.extraPipPackages .Values.web.extraPipPackages }}
{{- $extraVolumeMounts := .Values.web.extraVolumeMounts }}
{{- $volumeMounts := include "airflow.volumeMounts" (dict "Release" .Release "Values" .Values "extraPipPackages" $extraPipPackages "extraVolumeMounts" $extraVolumeMounts) }}
{{- $extraVolumes := .Values.web.extraVolumes }}
{{- $volumes := include "airflow.volumes" (dict "Release" .Release "Values" .Values "extraPipPackages" $extraPipPackages "extraVolumes" $extraVolumes "extraVolumeMounts" $extraVolumeMounts) }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "airflow.fullname" . }}-web
  {{- if .Values.web.annotations }}
  annotations:
    {{- toYaml .Values.web.annotations | nindent 4 }}
  {{- end }}
  labels:
    app: {{ include "airflow.labels.app" . }}
    component: web
    chart: {{ include "airflow.labels.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    {{- if .Values.web.labels }}
    {{- toYaml .Values.web.labels | nindent 4 }}
    {{- end }}
spec:
  replicas: {{ .Values.web.replicas }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      ## multiple web pods can safely run concurrently
      maxSurge: 25%
      maxUnavailable: 0
  selector:
    matchLabels:
      app: {{ include "airflow.labels.app" . }}
      component: web
      release: {{ .Release.Name }}
  template:
    metadata:
      annotations:
        checksum/secret-config-envs: {{ include (print $.Template.BasePath "/config/secret-config-envs.yaml") . | sha256sum }}
        checksum/secret-local-settings: {{ include (print $.Template.BasePath "/config/secret-local-settings.yaml") . | sha256sum }}
        {{- if and (.Values.web.webserverConfig.enabled) (not .Values.web.webserverConfig.existingSecret) }}
        checksum/config-webserver-config: {{ include (print $.Template.BasePath "/config/secret-webserver-config.yaml") . | sha256sum }}
        {{- end }}
        {{- if .Values.airflow.podAnnotations }}
        {{- toYaml .Values.airflow.podAnnotations | nindent 8 }}
        {{- end }}
        {{- if .Values.web.podAnnotations }}
        {{- toYaml .Values.web.podAnnotations | nindent 8 }}
        {{- end }}
        {{- if .Values.web.safeToEvict }}
        cluster-autoscaler.kubernetes.io/safe-to-evict: "true"
        {{- end }}
      labels:
        app: {{ include "airflow.labels.app" . }}
        component: web
        release: {{ .Release.Name }}
        {{- if .Values.web.podLabels }}
        {{- toYaml .Values.web.podLabels | nindent 8 }}
        {{- end }}
    spec:
      restartPolicy: Always
      {{- if .Values.airflow.image.pullSecret }}
      imagePullSecrets:
        - name: {{ .Values.airflow.image.pullSecret }}
      {{- end }}
      {{- if $podNodeSelector }}
      nodeSelector:
        {{- $podNodeSelector | nindent 8 }}
      {{- end }}
      {{- if $podTopologySpreadConstraints }}
      topologySpreadConstraints:
        {{- $podTopologySpreadConstraints | nindent 8 }}
      {{- end }}
      {{- if $podAffinity }}
      affinity:
        {{- $podAffinity | nindent 8 }}
      {{- end }}
      {{- if $podTolerations }}
      tolerations:
        {{- $podTolerations | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "airflow.serviceAccountName" . }}
      {{- if $podSecurityContext }}
      securityContext:
        {{- $podSecurityContext | nindent 8 }}
      {{- end }}
      initContainers:
        {{- if $extraPipPackages }}
        {{- include "airflow.init_container.install_pip_packages" (dict "Release" .Release "Values" .Values "extraPipPackages" $extraPipPackages) | indent 8 }}
        {{- end }}
        {{- if .Values.dags.gitSync.enabled }}
        {{- include "airflow.container.git_sync" (dict "Release" .Release "Values" .Values "sync_one_time" "true") | indent 8 }}
        {{- end }}
        {{- include "airflow.init_container.check_db" (dict "Release" .Release "Values" .Values "volumeMounts" $volumeMounts) | indent 8 }}
        {{- include "airflow.init_container.wait_for_db_migrations" (dict "Release" .Release "Values" .Values "volumeMounts" $volumeMounts) | indent 8 }}
        {{- if .Values.airflow.extraInitContainers }}
        {{- toYaml .Values.airflow.extraInitContainers | nindent 8 }}
        {{- end }}
        {{- if .Values.web.extraInitContainers }}
        {{- toYaml .Values.web.extraInitContainers | nindent 8 }}
        {{- end }}
      containers:
        - name: airflow-web
          {{- include "airflow.image" . | indent 10 }}
          resources:
            {{- toYaml .Values.web.resources | nindent 12 }}
          ports:
            - name: web
              containerPort: 8080
              protocol: TCP
          envFrom:
            {{- include "airflow.envFrom" . | indent 12 }}
          env:
            {{- include "airflow.env" . | indent 12 }}
          command:
            {{- include "airflow.command" . | indent 12 }}
          args:
            - "bash"
            - "-c"
            - "exec airflow webserver"
          {{- if .Values.web.livenessProbe.enabled }}
          livenessProbe:
            initialDelaySeconds: {{ .Values.web.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.web.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.web.livenessProbe.timeoutSeconds }}
            failureThreshold: {{ .Values.web.livenessProbe.failureThreshold }}
            httpGet:
              scheme: {{ include "airflow.web.scheme" . }}
              {{- $airflowUrl := .Values.airflow.config.AIRFLOW__WEBSERVER__BASE_URL | default "" | printf "%s/health" | urlParse }}
              path: {{ get $airflowUrl "path" }}
              port: web
          {{- end }}
          {{- if .Values.web.readinessProbe.enabled }}
          readinessProbe:
            initialDelaySeconds: {{ .Values.web.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.web.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.web.readinessProbe.timeoutSeconds }}
            failureThreshold: {{ .Values.web.readinessProbe.failureThreshold }}
            httpGet:
              scheme: {{ include "airflow.web.scheme" . }}
              {{- $airflowUrl := .Values.airflow.config.AIRFLOW__WEBSERVER__BASE_URL | default "" | printf "%s/health" | urlParse }}
              path: {{ get $airflowUrl "path" }}
              port: web
          {{- end }}
          volumeMounts:
            {{- $volumeMounts | indent 12 }}
            {{- if .Values.web.webserverConfig.enabled }}
            - name: webserver-config
              mountPath: /opt/airflow/webserver_config.py
              subPath: webserver_config.py
              readOnly: true
            {{- end }}
        {{- if .Values.dags.gitSync.enabled }}
        {{- include "airflow.container.git_sync" . | indent 8 }}
        {{- end }}
        {{- if .Values.airflow.extraContainers }}
        {{- toYaml .Values.airflow.extraContainers | nindent 8 }}
        {{- end }}
        {{- if .Values.web.extraContainers }}
        {{- toYaml .Values.web.extraContainers | nindent 8 }}
        {{- end }}
      volumes:
        {{- $volumes | indent 8 }}
        {{- if .Values.web.webserverConfig.enabled }}
        - name: webserver-config
          secret:
            {{- if .Values.web.webserverConfig.existingSecret }}
            secretName: {{ .Values.web.webserverConfig.existingSecret }}
            {{- else }}
            secretName: {{ include "airflow.fullname" . }}-webserver-config
            {{- end }}
            defaultMode: 0644
        {{- end }}
