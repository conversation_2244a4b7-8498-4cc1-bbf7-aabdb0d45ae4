{{- if and (.Values.ingress.enabled) (eq .Values.ingress.apiVersion "networking.k8s.io/v1") }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "airflow.fullname" . }}-web
  {{- if .Values.ingress.web.annotations }}
  annotations:
    {{- toYaml .Values.ingress.web.annotations | nindent 4 }}
  {{- end }}
  labels:
    app: {{ include "airflow.labels.app" . }}
    component: web
    chart: {{ include "airflow.labels.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    {{- if .Values.ingress.web.labels }}
    {{- toYaml .Values.ingress.web.labels | nindent 4 }}
    {{- end }}
spec:
  {{- if .Values.ingress.web.tls.enabled }}
  tls:
    - hosts:
        - {{ .Values.ingress.web.host }}
      {{- if .Values.ingress.web.tls.secretName }}
      secretName: {{ .Values.ingress.web.tls.secretName }}
      {{- end }}
  {{- end }}
  {{- if .Values.ingress.web.ingressClassName }}
  ingressClassName: {{ .Values.ingress.web.ingressClassName }}
  {{- end }}
  rules:
    - host: {{ .Values.ingress.web.host }}
      http:
        paths:
          {{- range .Values.ingress.web.precedingPaths }}
          - path: {{ .path }}
            pathType: ImplementationSpecific
            backend:
              service:
                name: {{ .serviceName }}
                port:
                  {{- if kindIs "string" .servicePort }}
                  name: {{ .servicePort }}
                  {{- else }}
                  number: {{ .servicePort }}
                  {{- end }}
          {{- end }}
          - path: {{ .Values.ingress.web.path }}
            pathType: ImplementationSpecific
            backend:
              service:
                name: {{ include "airflow.fullname" . }}-web
                port:
                  name: web
          {{- range .Values.ingress.web.succeedingPaths }}
          - path: {{ .path }}
            pathType: ImplementationSpecific
            backend:
              service:
                name: {{ .serviceName }}
                port:
                  {{- if kindIs "string" .servicePort }}
                  name: {{ .servicePort }}
                  {{- else }}
                  number: {{ .servicePort }}
                  {{- end }}
          {{- end }}

{{- end }}
