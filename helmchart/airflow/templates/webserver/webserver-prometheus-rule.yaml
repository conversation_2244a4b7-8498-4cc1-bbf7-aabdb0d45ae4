{{- if .Values.prometheusRule.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: {{ include "airflow.fullname" . }}
  labels:
    app: {{ include "airflow.labels.app" . }}
    component: web
    chart: {{ include "airflow.labels.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    {{- if .Values.prometheusRule.additionalLabels }}
    {{- toYaml .Values.prometheusRule.additionalLabels | nindent 4 }}
    {{- end }}
spec:
  groups:
    {{- toYaml .Values.prometheusRule.groups | nindent 4 }}
{{- end }}
