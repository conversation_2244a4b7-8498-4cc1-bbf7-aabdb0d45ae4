{{- if .Values.web.podDisruptionBudget.enabled }}
apiVersion: {{ .Values.web.podDisruptionBudget.apiVersion }}
kind: PodDisruptionBudget
metadata:
  name: {{ include "airflow.fullname" . }}-web
  labels:
    app: {{ include "airflow.labels.app" . }}
    component: web
    chart: {{ include "airflow.labels.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  {{- if .Values.web.podDisruptionBudget.maxUnavailable }}
  maxUnavailable: {{ .Values.web.podDisruptionBudget.maxUnavailable }}
  {{- end }}
  {{- if .Values.web.podDisruptionBudget.minAvailable }}
  minAvailable: {{ .Values.web.podDisruptionBudget.minAvailable }}
  {{- end }}
  selector:
    matchLabels:
      app: {{ include "airflow.labels.app" . }}
      component: web
      release: {{ .Release.Name }}
{{- end }}
