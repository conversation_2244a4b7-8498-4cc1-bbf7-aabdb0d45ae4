{{- if include "airflow.executor.kubernetes_like" . }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "airflow.fullname" . }}-pod-template
  labels:
    app: {{ include "airflow.labels.app" . }}
    chart: {{ include "airflow.labels.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
data:
  pod_template.yaml: |-
    {{- if .Values.airflow.kubernetesPodTemplate.stringOverride }}
    {{- .Values.airflow.kubernetesPodTemplate.stringOverride | nindent 4 }}
    {{- else }}
    {{- tpl (.Files.Get "files/pod_template.kubernetes-helm-yaml") . | nindent 4 }}
    {{- end }}
{{- end }}