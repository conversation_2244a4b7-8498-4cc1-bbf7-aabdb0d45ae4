{{- if and (.Values.airflow.dbMigrations.enabled) (.Values.airflow.dbMigrations.runAsJob) }}
{{- $podNodeSelector := include "airflow.podNodeSelector" (dict "Release" .Release "Values" .Values "nodeSelector" .Values.airflow.dbMigrations.nodeSelector) }}
{{- $podTopologySpreadConstraints := include "airflow.podTopologySpreadConstraints" (dict "Release" .Release "Values" .Values "topologySpreadConstraints" .Values.airflow.dbMigrations.topologySpreadConstraints) }}
{{- $podAffinity := include "airflow.podAffinity" (dict "Release" .Release "Values" .Values "affinity" .Values.airflow.dbMigrations.affinity) }}
{{- $podTolerations := include "airflow.podTolerations" (dict "Release" .Release "Values" .Values "tolerations" .Values.airflow.dbMigrations.tolerations) }}
{{- $podSecurityContext := include "airflow.podSecurityContext" (dict "Release" .Release "Values" .Values "securityContext" .Values.airflow.dbMigrations.securityContext) }}
{{- $extraPipPackages := .Values.airflow.extraPipPackages }}
{{- $volumeMounts := include "airflow.volumeMounts" (dict "Release" .Release "Values" .Values "extraPipPackages" $extraPipPackages) }}
{{- $volumes := include "airflow.volumes" (dict "Release" .Release "Values" .Values "extraPipPackages" $extraPipPackages) }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "airflow.fullname" . }}-db-migrations
  labels:
    app: {{ include "airflow.labels.app" . }}
    component: db-migrations
    chart: {{ include "airflow.labels.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    {{- if .Values.airflow.dbMigrations.labels }}
    {{- toYaml .Values.airflow.dbMigrations.labels | nindent 4 }}
    {{- end }}
  annotations:
    helm.sh/hook: post-install,post-upgrade
    helm.sh/hook-weight: "-100"
    helm.sh/hook-delete-policy: before-hook-creation
    {{- if .Values.airflow.dbMigrations.annotations }}
    {{- toYaml .Values.airflow.dbMigrations.annotations | nindent 4 }}
    {{- end }}
spec:
  template:
    metadata:
      annotations:
        checksum/secret-config-envs: {{ include (print $.Template.BasePath "/config/secret-config-envs.yaml") . | sha256sum }}
        checksum/secret-local-settings: {{ include (print $.Template.BasePath "/config/secret-local-settings.yaml") . | sha256sum }}
        checksum/db-migrations-script: {{ include "airflow.db_migrations.db_migrations.py" . | sha256sum }}
        {{- if .Values.airflow.podAnnotations }}
        {{- toYaml .Values.airflow.podAnnotations | nindent 8 }}
        {{- end }}
        {{- if .Values.airflow.dbMigrations.podAnnotations }}
        {{- toYaml .Values.airflow.dbMigrations.podAnnotations | nindent 8 }}
        {{- end }}
        {{- if .Values.airflow.dbMigrations.safeToEvict }}
        cluster-autoscaler.kubernetes.io/safe-to-evict: "true"
        {{- end }}
      labels:
        app: {{ include "airflow.labels.app" . }}
        component: db-migrations
        release: {{ .Release.Name }}
        {{- if .Values.airflow.dbMigrations.podLabels }}
        {{- toYaml .Values.airflow.dbMigrations.podLabels | nindent 8 }}
        {{- end }}
    spec:
      restartPolicy: OnFailure
      {{- if .Values.airflow.image.pullSecret }}
      imagePullSecrets:
        - name: {{ .Values.airflow.image.pullSecret }}
      {{- end }}
      {{- if $podNodeSelector }}
      nodeSelector:
        {{- $podNodeSelector | nindent 8 }}
      {{- end }}
      {{- if $podTopologySpreadConstraints }}
      topologySpreadConstraints:
        {{- $podTopologySpreadConstraints | nindent 8 }}
      {{- end }}
      {{- if $podAffinity }}
      affinity:
        {{- $podAffinity | nindent 8 }}
      {{- end }}
      {{- if $podTolerations }}
      tolerations:
        {{- $podTolerations | nindent 8 }}
      {{- end }}
      {{- if $podSecurityContext }}
      securityContext:
        {{- $podSecurityContext | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "airflow.serviceAccountName" . }}
      initContainers:
        {{- if $extraPipPackages }}
        {{- include "airflow.init_container.install_pip_packages" (dict "Release" .Release "Values" .Values "extraPipPackages" $extraPipPackages) | indent 8 }}
        {{- end }}
        {{- if .Values.dags.gitSync.enabled }}
        ## git-sync is included so "airflow plugins" & "python packages" can be stored in the dags repo
        {{- include "airflow.container.git_sync" (dict "Release" .Release "Values" .Values "sync_one_time" "true") | indent 8 }}
        {{- end }}
        {{- include "airflow.init_container.check_db" (dict "Release" .Release "Values" .Values "volumeMounts" $volumeMounts) | indent 8 }}
      containers:
        - name: db-migrations
          {{- include "airflow.image" . | indent 10 }}
          resources:
            {{- toYaml .Values.airflow.dbMigrations.resources | nindent 12 }}
          envFrom:
            {{- include "airflow.envFrom" . | indent 12 }}
          env:
            {{- include "airflow.env" . | indent 12 }}
          command:
            {{- include "airflow.command" . | indent 12 }}
          args:
            - "python"
            - "-u"
            - "/mnt/scripts/db_migrations.py"
          volumeMounts:
            {{- $volumeMounts | indent 12 }}
            - name: scripts
              mountPath: /mnt/scripts
              readOnly: true
      volumes:
        {{- $volumes | indent 8 }}
        - name: scripts
          secret:
            secretName: {{ include "airflow.fullname" . }}-db-migrations
{{- end }}