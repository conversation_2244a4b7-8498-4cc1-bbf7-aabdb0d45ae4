{{- if .Values.scheduler.podDisruptionBudget.enabled }}
apiVersion: {{ .Values.scheduler.podDisruptionBudget.apiVersion }}
kind: PodDisruptionBudget
metadata:
  name: {{ include "airflow.fullname" . }}-scheduler
  labels:
    app: {{ include "airflow.labels.app" . }}
    component: scheduler
    chart: {{ include "airflow.labels.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  {{- if .Values.scheduler.podDisruptionBudget.maxUnavailable }}
  maxUnavailable: {{ .Values.scheduler.podDisruptionBudget.maxUnavailable }}
  {{- end }}
  {{- if .Values.scheduler.podDisruptionBudget.minAvailable }}
  minAvailable: {{ .Values.scheduler.podDisruptionBudget.minAvailable }}
  {{- end }}
  selector:
    matchLabels:
      app: {{ include "airflow.labels.app" . }}
      component: scheduler
      release: {{ .Release.Name }}
{{- end }}
