{{- if .Values.airflow.variables }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "airflow.fullname" . }}-sync-variables
  labels:
    app: {{ include "airflow.labels.app" . }}
    component: sync-variables
    chart: {{ include "airflow.labels.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
data:
  sync_variables.py: {{ include "airflow.sync.sync_variables.py" . | b64enc | quote }}
{{- end }}