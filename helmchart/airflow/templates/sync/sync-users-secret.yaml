{{- if .Values.airflow.users }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "airflow.fullname" . }}-sync-users
  labels:
    app: {{ include "airflow.labels.app" . }}
    component: sync-users
    chart: {{ include "airflow.labels.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
data:
  sync_users.py: {{ include "airflow.sync.sync_users.py" . | b64enc | quote }}
{{- end }}