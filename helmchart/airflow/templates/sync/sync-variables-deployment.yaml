{{- if and (.Values.airflow.variables) (.Values.airflow.variablesUpdate) }}
{{- $podNodeSelector := include "airflow.podNodeSelector" (dict "Release" .Release "Values" .Values "nodeSelector" .Values.airflow.sync.nodeSelector) }}
{{- $podTopologySpreadConstraints := include "airflow.podTopologySpreadConstraints" (dict "Release" .Release "Values" .Values "topologySpreadConstraints" .Values.airflow.sync.topologySpreadConstraints) }}
{{- $podAffinity := include "airflow.podAffinity" (dict "Release" .Release "Values" .Values "affinity" .Values.airflow.sync.affinity) }}
{{- $podTolerations := include "airflow.podTolerations" (dict "Release" .Release "Values" .Values "tolerations" .Values.airflow.sync.tolerations) }}
{{- $podSecurityContext := include "airflow.podSecurityContext" (dict "Release" .Release "Values" .Values "securityContext" .Values.airflow.sync.securityContext) }}
{{- $extraPipPackages := .Values.airflow.extraPipPackages }}
{{- $volumeMounts := include "airflow.volumeMounts" (dict "Release" .Release "Values" .Values "extraPipPackages" $extraPipPackages) }}
{{- $volumes := include "airflow.volumes" (dict "Release" .Release "Values" .Values "extraPipPackages" $extraPipPackages) }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "airflow.fullname" . }}-sync-variables
  labels:
    app: {{ include "airflow.labels.app" . }}
    component: sync-variables
    chart: {{ include "airflow.labels.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    {{- if .Values.airflow.sync.labels }}
    {{- toYaml .Values.airflow.sync.labels | nindent 4 }}
    {{- end }}
  {{- if .Values.airflow.sync.annotations }}
  annotations:
    {{- toYaml .Values.airflow.sync.annotations | nindent 4 }}
  {{- end }}
spec:
  replicas: 1
  strategy:
    ## only 1 replica should run at a time
    type: Recreate
  selector:
    matchLabels:
      app: {{ include "airflow.labels.app" . }}
      component: sync-variables
      release: {{ .Release.Name }}
  template:
    metadata:
      annotations:
        checksum/secret-config-envs: {{ include (print $.Template.BasePath "/config/secret-config-envs.yaml") . | sha256sum }}
        checksum/secret-local-settings: {{ include (print $.Template.BasePath "/config/secret-local-settings.yaml") . | sha256sum }}
        checksum/sync-variables-script: {{ include "airflow.sync.sync_variables.py" . | sha256sum }}
        {{- if .Values.airflow.podAnnotations }}
        {{- toYaml .Values.airflow.podAnnotations | nindent 8 }}
        {{- end }}
        {{- if .Values.airflow.sync.podAnnotations }}
        {{- toYaml .Values.airflow.sync.podAnnotations | nindent 8 }}
        {{- end }}
        {{- if .Values.airflow.sync.safeToEvict }}
        cluster-autoscaler.kubernetes.io/safe-to-evict: "true"
        {{- end }}
      labels:
        app: {{ include "airflow.labels.app" . }}
        component: sync-variables
        release: {{ .Release.Name }}
        {{- if .Values.airflow.sync.podLabels }}
        {{- toYaml .Values.airflow.sync.podLabels | nindent 8 }}
        {{- end }}
    spec:
      restartPolicy: Always
      {{- if .Values.airflow.image.pullSecret }}
      imagePullSecrets:
        - name: {{ .Values.airflow.image.pullSecret }}
      {{- end }}
      {{- if $podNodeSelector }}
      nodeSelector:
        {{- $podNodeSelector | nindent 8 }}
      {{- end }}
      {{- if $podTopologySpreadConstraints }}
      topologySpreadConstraints:
        {{- $podTopologySpreadConstraints | nindent 8 }}
      {{- end }}
      {{- if $podAffinity }}
      affinity:
        {{- $podAffinity | nindent 8 }}
      {{- end }}
      {{- if $podTolerations }}
      tolerations:
        {{- $podTolerations | nindent 8 }}
      {{- end }}
      {{- if $podSecurityContext }}
      securityContext:
        {{- $podSecurityContext | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "airflow.serviceAccountName" . }}
      initContainers:
        {{- if $extraPipPackages }}
        {{- include "airflow.init_container.install_pip_packages" (dict "Release" .Release "Values" .Values "extraPipPackages" $extraPipPackages) | indent 8 }}
        {{- end }}
        {{- if .Values.dags.gitSync.enabled }}
        ## git-sync is included so "airflow plugins" & "python packages" can be stored in the dags repo
        {{- include "airflow.container.git_sync" (dict "Release" .Release "Values" .Values "sync_one_time" "true") | indent 8 }}
        {{- end }}
        {{- include "airflow.init_container.check_db" (dict "Release" .Release "Values" .Values "volumeMounts" $volumeMounts) | indent 8 }}
        {{- include "airflow.init_container.wait_for_db_migrations" (dict "Release" .Release "Values" .Values "volumeMounts" $volumeMounts) | indent 8 }}
      containers:
        - name: sync-airflow-variables
          {{- include "airflow.image" . | indent 10 }}
          resources:
            {{- toYaml .Values.airflow.sync.resources | nindent 12 }}
          envFrom:
            {{- include "airflow.envFrom" . | indent 12 }}
          env:
            {{- include "airflow.env" . | indent 12 }}
          command:
            {{- include "airflow.command" . | indent 12 }}
          args:
            - "python"
            - "-u"
            - "/mnt/scripts/sync_variables.py"
          volumeMounts:
            {{- $volumeMounts | indent 12 }}
            - name: scripts
              mountPath: /mnt/scripts
              readOnly: true
            {{- if .Values.airflow.variablesTemplates }}
            - name: templates
              mountPath: "/mnt/templates"
              readOnly: true
            {{- end }}
        {{- if .Values.dags.gitSync.enabled }}
        ## git-sync is included so "airflow plugins" & "python packages" can be stored in the dags repo
        {{- include "airflow.container.git_sync" . | indent 8 }}
        {{- end }}
      volumes:
        {{- $volumes | indent 8 }}
        - name: scripts
          secret:
            secretName: {{ include "airflow.fullname" . }}-sync-variables
        {{- if .Values.airflow.variablesTemplates }}
        - name: templates
          projected:
            sources:
              {{- range $k, $v := .Values.airflow.variablesTemplates }}
              {{- if not (regexMatch "^[a-zA-Z_][a-zA-Z0-9_]*$" $k) }}
              {{ required "each key in `airflow.variablesTemplates` must match the regex: ^[a-zA-Z_][a-zA-Z0-9_]*$" nil }}
              {{- end }}
              {{- if eq ($v.kind | lower) "configmap" }}
              - configMap:
                  name: {{ $v.name | quote }}
                  items:
                    - key: {{ $v.key | quote }}
                      path: {{ $k | quote }}
              {{- else if eq ($v.kind | lower) "secret" }}
              - secret:
                  name: {{ $v.name | quote }}
                  items:
                    - key: {{ $v.key | quote }}
                      path: {{ $k | quote }}
              {{- else }}
              {{ required "each `kind` in `airflow.variablesTemplates` must be one of ['configmap', 'secret']!" nil }}
              {{- end }}
              {{- end }}
        {{- end }}
{{- end }}