{{- if .Values.rbac.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "airflow.fullname" . }}
  labels:
    app: {{ include "airflow.labels.app" . }}
    chart: {{ include "airflow.labels.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "airflow.fullname" . }}
subjects:
- kind: ServiceAccount
  name: {{ include "airflow.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
{{- end }}
