{{- if .Values.rbac.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ include "airflow.fullname" . }}
  labels:
    app: {{ include "airflow.labels.app" . }}
    chart: {{ include "airflow.labels.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
rules:
{{- if .Values.rbac.events }}
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - "get"
  - "list"
{{- end }}
{{- if .Values.rbac.secrets }}
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - "get"
  - "list"
  - "watch"
{{- end }}
- apiGroups:
  - ""
  resources:
  - pods
  verbs:
  - "create"
  - "get"
  - "delete"
  - "list"
  - "patch"
  - "watch"
- apiGroups:
  - ""
  resources:
  - "pods/log"
  verbs:
  - "get"
  - "list"
- apiGroups:
  - ""
  resources:
  - "pods/exec"
  verbs:
  - "create"
  - "get"
{{- end }}
