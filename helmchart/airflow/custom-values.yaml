airflow:
  executor: KubernetesExecutor
  usersUpdate: false
  connectionsUpdate: false
  variablesUpdate: false
  poolsUpdate: false
  image:
    repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/airflow
    tag: 2.8.4-python3.9
    pullPolicy: IfNotPresent
  config:
    logging:
      remote_logging: "True"
      remote_base_log_folder: "s3://madhav-airflow-logging-bucket/logs"
      remote_log_conn_id: "s3_logging"
      logging_level: "INFO"
    email:
      email_backend: "airflow.utils.email.send_email_smtp"
    smtp:
      smtp_host: "smtp.gmail.com"
      smtp_starttls: "True"
      smtp_ssl: "False"
      smtp_port: "587"
      smtp_mail_from: "<EMAIL>"
  connections:
    - id: my_ssh_connection
      type: ssh
      description: SSH Connection to Server
      host: ***********
      login: madhav.prajapati
      port: 22
      extra: |
        {
          "key_file": "/opt/airflow/ssh/id_rsa",
          "no_host_key_check": "true"
        }
    
  defaultAffinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: nodepool
                operator: In
                values:
                  - staging-solvei8-amd-spot-all
                  - staging-solvei8-amd-on-demand
  defaultTolerations:
    - key: "workload"
      operator: "Equal"
      value: "spot-all"
      effect: "NoSchedule"
    - key: "workload"
      operator: "Equal"
      value: "ondemand"
      effect: "NoSchedule"

  extraPipPackages:
    - "pygsheets==2.0.6"
    - "google-auth-oauthlib>=0.4.1"
    - "google-auth-httplib2>=0.0.4"
  extraEnv:
    - name: AIRFLOW__API__AUTH_BACKENDS
      value: 'airflow.api.auth.backend.basic_auth'
    - name: "AIRFLOW__WEBSERVER__EXPOSE_CONFIG"
      value: "True"
    - name: AIRFLOW__WEBSERVER__BASE_URL
      value: "https://airflow.strawmine.com"
    - name: AIRFLOW__WEBSERVER__ENABLE_PROXY_FIX
      value: "True"
    - name: AIRFLOW__LOGGING__REMOTE_LOGGING
      value: "True"
    - name: AIRFLOW__LOGGING__REMOTE_BASE_LOG_FOLDER
      value: "s3://madhav-airflow-logging-bucket/logs"
    - name: AIRFLOW__LOGGING__REMOTE_LOG_CONN_ID
      value: "s3_logging"
    - name: AWS_DEFAULT_REGION
      value: "ap-southeast-1"
    # s3 secrets
    - name: AWS_ACCESS_KEY_ID
      valueFrom:
        secretKeyRef:
          name: airflow-s3-logging-secret
          key: AWS_ACCESS_KEY_ID
    - name: AWS_SECRET_ACCESS_KEY
      valueFrom:
        secretKeyRef:
          name: airflow-s3-logging-secret
          key: AWS_SECRET_ACCESS_KEY
    - name: AIRFLOW__WEBSERVER__AUTHENTICATE
      value: "True"
    - name: AIRFLOW__WEBSERVER__AUTH_BACKEND
      value: "airflow.providers.google.cloud.auth_manager.backends.google_oauth"
    - name: AIRFLOW__WEBSERVER__RBAC
      value: "True"
  extraVolumeMounts:
    - name: ssh-key
      mountPath: /opt/airflow/ssh
      readOnly: true
  extraVolumes:
    - name: ssh-key
      secret:
        secretName: airflow-ssh-secret
        defaultMode: 0600
        items:
          - key: SSH_PRIVATE_KEY
            path: id_rsa
  dbMigrations:
    runAsJob: true
    resources:
      limits:
        cpu: 200m
        memory: 650Mi
      requests:
        cpu: 25m
        memory: 200Mi
scheduler:
  replicas: 1
  affinity: 
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: "nodepool"
                operator: In
                values:
                  - "staging-solvei8-amd-on-demand-general-purpose"
  tolerations: 
    - key: "workload"
      operator: "Equal"
      value: "ondemand-general"
      effect: "NoSchedule"

  resources:
    limits:
      cpu: 500m
      memory: 1200Mi
    requests:
      cpu: 300m
      memory: 600Mi
web:
  webserverConfig:
    enabled: true
    stringOverride: |
      from flask_appbuilder.security.manager import AUTH_OAUTH
      AUTH_TYPE = AUTH_OAUTH
      AUTH_USER_REGISTRATION = True
      AUTH_USER_REGISTRATION_ROLE = 'Viewer'
      OAUTH_PROVIDERS = [{
          'name':'google',
          'token_key':'access_token',
          'icon':'fa-google',
          'remote_app': {
             'api_base_url':'https://www.googleapis.com/oauth2/v2/',
             'client_kwargs':{
               'scope': 'email profile'
             },
             'access_token_url':'https://accounts.google.com/o/oauth2/token',
             'authorize_url':'https://accounts.google.com/o/oauth2/auth',
             'request_token_url': None,
             'client_id': '************-lt35ap83jnf7dcfjq1jn262hl1kn3abn.apps.googleusercontent.com',
             'client_secret': '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',
       }
      }]
    env:
    - name: AIRFLOW__WEBSERVER__BASE_URL
      value: "https://airflow.strawmine.com"

  resources:
    limits:
      cpu: "300m"
      memory: 2048Mi
    requests:
      cpu: 100m
      memory: 1500Mi
  affinity: 
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: "nodepool"
                operator: In
                values:
                  - "staging-solvei8-amd-on-demand-general-purpose"

  tolerations:
  - key: "workload"
    operator: "Equal"
    value: "ondemand-general"
    effect: "NoSchedule"
    
  livenessProbe:
    initialDelaySeconds: 20
    timeoutSeconds: 10
    periodSeconds: 10
    failureThreshold: 10
  readinessProbe:
    initialDelaySeconds: 20
    timeoutSeconds: 10
    periodSeconds: 10
    failureThreshold: 10
workers:
  enabled: false
triggerer:
  enabled: true
  replicas: 2
  resources:
    limits:
      cpu: 250m
      memory: 500Mi
    requests:
      cpu: 150m
      memory: 300Mi
flower:
  enabled: false
dags:
  gitSync:
    image:
      repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/git-sync
      tag: v3.6.9
      pullPolicy: IfNotPresent
    enabled: true
    repo: "https://bitbucket.org/ncinga/zilingo-airflow-devops.git"
    repoSubPath: "solvei8_dags"
    branch: madhav
    httpSecret: "airflow-bitbucket-secret"
    httpSecretPasswordKey: "app-password"
ingress:
  enabled: true
  web:
    annotations:
      cert-manager.io/cluster-issuer: letsencrypt-issuer
      nginx.ingress.kubernetes.io/configuration-snippet: |
        more_set_headers "X-Content-Type-Options: nosniff";
        more_set_headers "X-Frame-Options: SAMEORIGIN";
        more_set_headers "Referrer-Policy: strict-origin-when-cross-origin";
        more_set_headers "Strict-Transport-Security: max-age=********; includeSubDomains; preload";
        more_set_headers "Cross-Origin-Resource-Policy: same-site";
    host: "airflow.strawmine.com"
    ingressClassName: "external-nginx"
    tls:
      enabled: true
      secretName: "airflow.strawmine.com-tls"
serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/KarpenterControllerRole-staging-solvei8-eks
pgbouncer:
  enabled: true
  image:
    repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/pgbouncer
    tag: 1.22.1-patch.0
    pullPolicy: IfNotPresent
  resources:
    limits:
      cpu: 100m
      memory: 100Mi
    requests:
      cpu: 50m
      memory: 20Mi
  affinity: 
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: "nodepool"
                operator: In
                values:
                  - "staging-solvei8-amd-on-demand-general-purpose"
  tolerations: 
    - key: "workload"
      operator: "Equal"
      value: "ondemand-general"
      effect: "NoSchedule"

postgresql:
  image:
    registry: ************.dkr.ecr.ap-southeast-1.amazonaws.com
    repository: postgresql-bitnami
    tag: 11.22-patch.0
    pullPolicy: IfNotPresent
  enabled: true
  persistence:
    enabled: true
    storageClass: "ebs-sc"
    accessModes:
      - ReadWriteOnce
    size: 8Gi
  resources:
    limits:
      cpu: 250m
      memory: 256Mi
    requests:
      cpu: 100m
      memory: 100Mi
  master:
    affinity: 
      nodeAffinity:
        requiredDuringSchedulingIgnoredDuringExecution:
          nodeSelectorTerms:
            - matchExpressions:
                - key: "nodepool"
                  operator: In
                  values:
                    - "staging-solvei8-amd-on-demand-general-purpose"
    tolerations: 
      - key: "workload"
        operator: "Equal"
        value: "ondemand-general"
        effect: "NoSchedule"

redis:
  enabled: false
