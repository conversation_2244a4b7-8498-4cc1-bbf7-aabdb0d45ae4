{{- if (include "postgresql.createSecret" .) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "postgresql.fullname" . }}
  labels:
    app: {{ template "postgresql.name" . }}
    chart: {{ template "postgresql.chart" . }}
    release: {{ .Release.Name | quote }}
    heritage: {{ .Release.Service | quote }}
type: Opaque
data:
  {{- if and .Values.postgresqlPostgresPassword (not (eq .Values.postgresqlUsername "postgres")) }}
  postgresql-postgres-password: {{ include "postgresql.postgres.password" . | b64enc | quote }}
  {{- end }}
  postgresql-password: {{ include "postgresql.password" . | b64enc | quote }}
  {{- if .Values.replication.enabled }}
  postgresql-replication-password: {{ include "postgresql.replication.password" . | b64enc | quote }}
  {{- end }}
  {{- if (and .Values.ldap.enabled .Values.ldap.bind_password)}}
  postgresql-ldap-password: {{ .Values.ldap.bind_password | b64enc | quote }}
  {{- end }}
{{- end -}}
