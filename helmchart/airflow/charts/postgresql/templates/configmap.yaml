{{ if and (or (.Files.Glob "files/postgresql.conf") (.Files.Glob "files/pg_hba.conf") .Values.postgresqlConfiguration .Values.pgHbaConfiguration) (not .Values.configurationConfigMap) }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "postgresql.fullname" . }}-configuration
  labels:
    app: {{ template "postgresql.name" . }}
    chart: {{ template "postgresql.chart" . }}
    release: {{ .Release.Name | quote }}
    heritage: {{ .Release.Service | quote }}
data:
{{- if (.Files.Glob "files/postgresql.conf") }}
{{ (.Files.Glob "files/postgresql.conf").AsConfig | indent 2 }}
{{- else if .Values.postgresqlConfiguration }}
  postgresql.conf: |
{{- range $key, $value := default dict .Values.postgresqlConfiguration }}
    {{ $key | snakecase }}={{ $value }}
{{- end }}
{{- end }}
{{- if (.Files.Glob "files/pg_hba.conf") }}
{{ (.Files.Glob "files/pg_hba.conf").AsConfig | indent 2 }}
{{- else if .Values.pgHbaConfiguration }}
  pg_hba.conf: |
{{ .Values.pgHbaConfiguration | indent 4 }}
{{- end }}
{{ end }}
