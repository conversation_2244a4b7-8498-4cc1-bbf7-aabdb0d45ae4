{{- if .Values.networkPolicy.enabled }}
kind: NetworkPolicy
apiVersion: {{ template "postgresql.networkPolicy.apiVersion" . }}
metadata:
  name: {{ template "postgresql.fullname" . }}
  labels:
    app: {{ template "postgresql.name" . }}
    chart: {{ template "postgresql.chart" . }}
    release: {{ .Release.Name | quote }}
    heritage: {{ .Release.Service | quote }}
spec:
  podSelector:
    matchLabels:
      app: {{ template "postgresql.name" . }}
      release: {{ .Release.Name | quote }}
  ingress:
    # Allow inbound connections
    - ports:
        - port: {{ template "postgresql.port" . }}
      {{- if not .Values.networkPolicy.allowExternal }}
      from:
        - podSelector:
            matchLabels:
              {{ template "postgresql.fullname" . }}-client: "true"
          {{- if .Values.networkPolicy.explicitNamespacesSelector }}
          namespaceSelector:
{{ toYaml .Values.networkPolicy.explicitNamespacesSelector | indent 12 }}
          {{- end }}
        - podSelector:
            matchLabels:
              app: {{ template "postgresql.name" . }}
              release: {{ .Release.Name | quote }}
              role: slave
      {{- end }}
    # Allow prometheus scrapes
    - ports:
        - port: 9187
{{- end }}
