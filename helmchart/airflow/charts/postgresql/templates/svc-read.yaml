{{- if .Values.replication.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "postgresql.fullname" . }}-read
  labels:
    app: {{ template "postgresql.name" . }}
    chart: {{ template "postgresql.chart" . }}
    release: {{ .Release.Name | quote }}
    heritage: {{ .Release.Service | quote }}
{{- with .Values.service.annotations }}
  annotations:
{{ toYaml . | indent 4 }}
{{- end }}
spec:
  type: {{ .Values.service.type }}
  {{- if and .Values.service.loadBalancerIP (eq .Values.service.type "LoadBalancer") }}
  loadBalancerIP: {{ .Values.service.loadBalancerIP }}
  {{- end }}
  ports:
    - name: tcp-postgresql
      port:  {{ template "postgresql.port" . }}
      targetPort: tcp-postgresql
      {{- if .Values.service.nodePort }}
      nodePort: {{ .Values.service.nodePort }}
      {{- end }}
  selector:
    app: {{ template "postgresql.name" . }}
    release: {{ .Release.Name | quote }}
    role: slave
{{- end }}
