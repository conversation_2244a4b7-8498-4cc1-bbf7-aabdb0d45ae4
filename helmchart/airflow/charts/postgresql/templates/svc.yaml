apiVersion: v1
kind: Service
metadata:
  name: {{ template "postgresql.fullname" . }}
  labels:
    app: {{ template "postgresql.name" . }}
    chart: {{ template "postgresql.chart" . }}
    release: {{ .Release.Name | quote }}
    heritage: {{ .Release.Service | quote }}
{{- with .Values.service.annotations }}
  annotations:
{{ tpl (toYaml .) $ | indent 4 }}
{{- end }}
spec:
  type: {{ .Values.service.type }}
  {{- if and .Values.service.loadBalancerIP (eq .Values.service.type "LoadBalancer") }}
  loadBalancerIP: {{ .Values.service.loadBalancerIP }}
  {{- end }}
  {{- if and (eq .Values.service.type "LoadBalancer") .Values.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges:
  {{ with .Values.service.loadBalancerSourceRanges }}
{{ toYaml . | indent 4 }}
{{- end }}
  {{- end }}
  {{- if and (eq .Values.service.type "ClusterIP") .Values.service.clusterIP }}
  clusterIP: {{ .Values.service.clusterIP }}
  {{- end }}
  ports:
    - name: tcp-postgresql
      port: {{ template "postgresql.port" . }}
      targetPort: tcp-postgresql
      {{- if .Values.service.nodePort }}
      nodePort: {{ .Values.service.nodePort }}
      {{- end }}
  selector:
    app: {{ template "postgresql.name" . }}
    release: {{ .Release.Name | quote }}
    role: master
