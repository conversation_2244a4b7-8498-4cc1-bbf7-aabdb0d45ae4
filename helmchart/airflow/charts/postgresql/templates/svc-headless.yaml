apiVersion: v1
kind: Service
metadata:
  name: {{ template "postgresql.fullname" . }}-headless
  labels:
    app: {{ template "postgresql.name" . }}
    chart: {{ template "postgresql.chart" . }}
    release: {{ .Release.Name | quote }}
    heritage: {{ .Release.Service | quote }}
spec:
  type: ClusterIP
  clusterIP: None
  ports:
    - name: tcp-postgresql
      port: {{ template "postgresql.port" . }}
      targetPort: tcp-postgresql
  selector:
    app: {{ template "postgresql.name" . }}
    release: {{ .Release.Name | quote }}
