{{- if .Values.rbac.create -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ template "redis.fullname" . }}
  labels:
    app: {{ template "redis.name" . }}
    chart: {{ template "redis.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ template "redis.fullname" . }}
subjects:
- kind: ServiceAccount
  name: {{ template "redis.serviceAccountName" . }}
{{- end -}}
