apiVersion: v1
kind: Service
metadata:
  name: {{ template "redis.fullname" . }}-headless
  labels:
    app: {{ template "redis.name" . }}
    chart: {{ template "redis.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - name: redis
    port: {{ .Values.redisPort }}
    targetPort: redis
{{- if .Values.sentinel.enabled }}
  - name: redis-sentinel
    port: {{ .Values.sentinel.port }}
    targetPort: redis-sentinel
{{- end }}
  selector:
    app: {{ template "redis.name" . }}
    release: {{ .Release.Name }}
