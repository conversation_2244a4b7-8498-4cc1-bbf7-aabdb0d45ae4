{{- if .Values.podSecurityPolicy.create }}
apiVersion: {{ template "podSecurityPolicy.apiVersion" . }}
kind: PodSecurityPolicy
metadata:
  name: {{ template "redis.fullname" . }}
  labels:
    app: {{ template "redis.name" . }}
    chart: {{ template "redis.chart" . }}
    heritage: {{ .Release.Service }}
    release: {{ .Release.Name }}
spec:
  allowPrivilegeEscalation: false
  fsGroup:
    rule: 'MustRunAs'
    ranges:
      - min: {{ .Values.securityContext.fsGroup }}
        max: {{ .Values.securityContext.fsGroup }}
  hostIPC: false
  hostNetwork: false
  hostPID: false
  privileged: false
  readOnlyRootFilesystem: false
  requiredDropCapabilities:
    - ALL
  runAsUser:
    rule: 'MustRunAs'
    ranges:
      - min: {{ .Values.securityContext.runAsUser }}
        max: {{ .Values.securityContext.runAsUser }}
  seLinux:
    rule: 'RunAsAny'
  supplementalGroups:
    rule: 'MustRunAs'
    ranges:
      - min: {{ .Values.securityContext.runAsUser }}
        max: {{ .Values.securityContext.runAsUser }}
  volumes:
    - 'configMap'
    - 'secret'
    - 'emptyDir'
    - 'persistentVolumeClaim'
{{- end }}
