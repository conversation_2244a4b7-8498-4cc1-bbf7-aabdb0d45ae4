{{- if .Values.sentinel.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "redis.fullname" . }}
  labels:
    app: {{ template "redis.name" . }}
    chart: {{ template "redis.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    {{- if .Values.sentinel.service.labels }}
    {{ toYaml .Values.sentinel.service.labels | nindent 4 }}
    {{- end }}
{{- if .Values.sentinel.service.annotations }}
  annotations:
{{ toYaml .Values.sentinel.service.annotations | indent 4 }}
{{- end }}
spec:
  type: {{ .Values.sentinel.service.type }}
  {{ if eq .Values.sentinel.service.type "LoadBalancer" -}} {{ if .Values.sentinel.service.loadBalancerIP }}
  loadBalancerIP: {{ .Values.sentinel.service.loadBalancerIP }}
  {{ end -}}
  {{- end -}}
  ports:
  - name: redis
    port: {{ .Values.sentinel.service.redisPort }}
    targetPort: redis
    {{- if .Values.sentinel.service.redisNodePort }}
    nodePort: {{ .Values.sentinel.service.redisNodePort }}
    {{- end }}
  - name: redis-sentinel
    port: {{ .Values.sentinel.service.sentinelPort }}
    targetPort: redis-sentinel
    {{- if .Values.sentinel.service.sentinelNodePort }}
    nodePort: {{ .Values.sentinel.service.sentinelNodePort }}
    {{- end }}
  selector:
    app: {{ template "redis.name" . }}
    release: {{ .Release.Name }}
{{- end }}
