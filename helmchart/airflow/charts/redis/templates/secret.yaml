{{- if and .Values.usePassword (not .Values.existingSecret) -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "redis.fullname" . }}
  labels:
    app: {{ template "redis.name" . }}
    chart: {{ template "redis.chart" . }}
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
type: Opaque
data:
  redis-password: {{ include "redis.password" . | b64enc | quote }}
{{- end -}}
