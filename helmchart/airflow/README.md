# Airflow Helm Chart Configuration

This document provides a comprehensive overview of the Airflow deployment configuration using the official Airflow Helm chart. It is based on the `custom-values.yaml` file and explains the role of each component and the specific configurations applied.

## Table of Contents
1.  [Airflow Components Overview](#airflow-components-overview)
2.  [Configuration Details (`custom-values.yaml`)](#configuration-details-custom-valuesyaml)
    - [Global Airflow Settings (`airflow`)](#global-airflow-settings-airflow)
    - [Scheduler (`scheduler`)](#scheduler-scheduler)
    - [Webserver (`web`)](#webserver-web)
    - [Workers (`workers`)](#workers-workers)
    - [Triggerer (`triggerer`)](#triggerer-triggerer)
    - [Flower (`flower`)](#flower-flower)
    - [DAGs (`dags`)](#dags-dags)
    - [Ingress (`ingress`)](#ingress-ingress)
    - [Service Account (`serviceAccount`)](#service-account-serviceaccount)
    - [PgBouncer (`pgbouncer`)](#pgbouncer-pgbouncer)
    - [PostgreSQL (`postgresql`)](#postgresql-postgresql)
    - [Redis (`redis`)](#redis-redis)

---

## Airflow Components Overview

Our Airflow environment is composed of several key services working together:

-   **Webserver**: The user interface (UI) for Airflow. It allows users to monitor DAGs, manage connections, and view logs.
-   **Scheduler**: The heart of Airflow. It monitors all DAGs and their dependencies, and triggers tasks whose dependencies have been met.
-   **Executor (`KubernetesExecutor`)**: The mechanism for running tasks. We use the `KubernetesExecutor`, which runs each task in a new, dedicated Kubernetes pod. This provides excellent isolation and resource management.
-   **Triggerer**: A separate process that supports "deferrable operators." These are operators that can release their worker slot while waiting for an external event, improving efficiency and reducing resource consumption.
-   **Metadata Database (`PostgreSQL`)**: Stores all metadata for Airflow, including DAG states, task instances, connections, and variables. We use a PostgreSQL database deployed within the same Helm release.
-   **PgBouncer**: A lightweight connection pooler for PostgreSQL. It sits between Airflow components and the PostgreSQL database to manage and limit the number of active connections, improving performance and stability.
-   **Git-Sync**: A sidecar container that runs alongside the scheduler and webserver pods. It automatically syncs DAGs from a specified Git repository, enabling a Git-based workflow for DAG management.

---

## Configuration Details (`custom-values.yaml`)

This section breaks down the `custom-values.yaml` file, explaining each configuration block.

### Global Airflow Settings (`airflow`)

This block defines settings that apply globally to most Airflow components.

```yaml
airflow:
  executor: KubernetesExecutor
  usersUpdate: false
  connectionsUpdate: false
  variablesUpdate: false
  poolsUpdate: false
  image:
    repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/airflow
    tag: 2.8.4-python3.9
    pullPolicy: IfNotPresent
  config:
    logging:
      remote_logging: "True"
      remote_base_log_folder: "s3://madhav-airflow-logging-bucket/logs"
      remote_log_conn_id: "s3_logging"
      logging_level: "INFO"
    email:
      email_backend: "airflow.utils.email.send_email_smtp"
    smtp:
      smtp_host: "smtp.gmail.com"
      smtp_starttls: "True"
      smtp_ssl: "False"
      smtp_port: "587"
      smtp_mail_from: "<EMAIL>"
  connections:
    - id: my_ssh_connection
      type: ssh
      description: SSH Connection to Server
      host: ***********
      login: madhav.prajapati
      port: 22
      extra: |
        {
          "key_file": "/opt/airflow/ssh/id_rsa",
          "no_host_key_check": "true"
        }
    
  defaultAffinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: nodepool
                operator: In
                values:
                  - staging-solvei8-amd-spot-all
                  - staging-solvei8-amd-on-demand
  defaultTolerations:
    - key: "workload"
      operator: "Equal"
      value: "spot-all"
      effect: "NoSchedule"
    - key: "workload"
      operator: "Equal"
      value: "ondemand"
      effect: "NoSchedule"

  extraPipPackages:
    - "pygsheets==2.0.6"
    - "google-auth-oauthlib>=0.4.1"
    - "google-auth-httplib2>=0.0.4"
  extraEnv:
    - name: AIRFLOW__API__AUTH_BACKENDS
      value: 'airflow.api.auth.backend.basic_auth'
    - name: "AIRFLOW__WEBSERVER__EXPOSE_CONFIG"
      value: "True"
    - name: AIRFLOW__WEBSERVER__BASE_URL
      value: "https://airflow.strawmine.com"
    - name: AIRFLOW__WEBSERVER__ENABLE_PROXY_FIX
      value: "True"
    - name: AIRFLOW__LOGGING__REMOTE_LOGGING
      value: "True"
    - name: AIRFLOW__LOGGING__REMOTE_BASE_LOG_FOLDER
      value: "s3://madhav-airflow-logging-bucket/logs"
    - name: AIRFLOW__LOGGING__REMOTE_LOG_CONN_ID
      value: "s3_logging"
    - name: AWS_DEFAULT_REGION
      value: "ap-southeast-1"
    # s3 secrets
    - name: AWS_ACCESS_KEY_ID
      valueFrom:
        secretKeyRef:
          name: airflow-s3-logging-secret
          key: AWS_ACCESS_KEY_ID
    - name: AWS_SECRET_ACCESS_KEY
      valueFrom:
        secretKeyRef:
          name: airflow-s3-logging-secret
          key: AWS_SECRET_ACCESS_KEY
    - name: AIRFLOW__WEBSERVER__AUTHENTICATE
      value: "True"
    - name: AIRFLOW__WEBSERVER__AUTH_BACKEND
      value: "airflow.providers.google.cloud.auth_manager.backends.google_oauth"
    - name: AIRFLOW__WEBSERVER__RBAC
      value: "True"
  extraVolumeMounts:
    - name: ssh-key
      mountPath: /opt/airflow/ssh
      readOnly: true
  extraVolumes:
    - name: ssh-key
      secret:
        secretName: airflow-ssh-secret
        defaultMode: 0600
        items:
          - key: SSH_PRIVATE_KEY
            path: id_rsa
  dbMigrations:
    runAsJob: true
    resources:
      limits:
        cpu: 200m
        memory: 650Mi
      requests:
        cpu: 25m
        memory: 200Mi
```

-   **`executor`**: Set to `KubernetesExecutor`, meaning each task runs as a separate pod.
-   **`usersUpdate`, `connectionsUpdate`, etc.**: These flags are disabled (`false`), meaning users, connections, variables, and pools are managed through the Airflow UI or API, not through Helm chart updates.
-   **`image`**: Specifies the Docker image for Airflow components, hosted in a private Amazon ECR repository.
-   **`config.logging`**: Configures remote logging to an S3 bucket, ensuring logs are persisted outside the task pods.
-   **`config.email` / `config.smtp`**: Sets up SMTP for sending email alerts from Airflow.
-   **`connections`**: Defines a pre-configured SSH connection named `my_ssh_connection`. The private key is sourced from a mounted volume.
-   **`defaultAffinity` & `defaultTolerations`**: These rules control where the Airflow pods are scheduled. Pods are scheduled on nodes in the `staging-solvei8-amd-spot-all` or `staging-solvei8-amd-on-demand` nodepools and can tolerate `spot-all` and `ondemand` taints.
-   **`extraPipPackages`**: A list of additional Python packages to be installed in the Airflow environment.
-   **`extraEnv`**: Sets crucial environment variables for all Airflow pods. This includes:
    -   Authentication settings (enabling basic auth for the API and Google OAuth for the UI).
    -   The webserver's base URL.
    -   Remote logging configuration.
    -   AWS credentials, which are securely sourced from a Kubernetes secret (`airflow-s3-logging-secret`).
-   **`extraVolumes` & `extraVolumeMounts`**: Defines and mounts a volume from a Kubernetes secret (`airflow-ssh-secret`) to provide an SSH private key for the pre-configured SSH connection.
-   **`dbMigrations`**: Configures the one-time job that runs database schema migrations during Helm upgrades.

### Scheduler (`scheduler`)

This block configures the Airflow scheduler.

```yaml
scheduler:
  replicas: 1
  affinity: 
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: "nodepool"
                operator: In
                values:
                  - "staging-solvei8-amd-on-demand-general-purpose"
  tolerations: 
    - key: "workload"
      operator: "Equal"
      value: "ondemand-general"
      effect: "NoSchedule"

  resources:
    limits:
      cpu: 500m
      memory: 1200Mi
    requests:
      cpu: 300m
      memory: 600Mi
```

-   **`replicas`**: Runs a single instance of the scheduler.
-   **`affinity` & `tolerations`**: Provides specific scheduling rules for the scheduler, ensuring it runs on nodes in the `staging-solvei8-amd-on-demand-general-purpose` nodepool.
-   **`resources`**: Defines the CPU and memory requests and limits for the scheduler pod.

### Webserver (`web`)

This block configures the Airflow web UI.

```yaml
web:
  webserverConfig:
    enabled: true
    stringOverride: |
      from flask_appbuilder.security.manager import AUTH_OAUTH
      AUTH_TYPE = AUTH_OAUTH
      AUTH_USER_REGISTRATION = True
      AUTH_USER_REGISTRATION_ROLE = 'Viewer'
      OAUTH_PROVIDERS = [{
          'name':'google',
          'token_key':'access_token',
          'icon':'fa-google',
          'remote_app': {
             'api_base_url':'https://www.googleapis.com/oauth2/v2/',
             'client_kwargs':{
               'scope': 'email profile'
             },
             'access_token_url':'https://accounts.google.com/o/oauth2/token',
             'authorize_url':'https://accounts.google.com/o/oauth2/auth',
             'request_token_url': None,
             'client_id': '************-lt35ap83jnf7dcfjq1jn262hl1kn3abn.apps.googleusercontent.com',
             'client_secret': '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',
       }
      }]
    env:
    - name: AIRFLOW__WEBSERVER__BASE_URL
      value: "https://airflow.strawmine.com"

  resources:
    limits:
      cpu: "300m"
      memory: 2048Mi
    requests:
      cpu: 100m
      memory: 1500Mi
  affinity: 
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: "nodepool"
                operator: In
                values:
                  - "staging-solvei8-amd-on-demand-general-purpose"

  tolerations:
  - key: "workload"
    operator: "Equal"
    value: "ondemand-general"
    effect: "NoSchedule"
    
  livenessProbe:
    initialDelaySeconds: 20
    timeoutSeconds: 10
    periodSeconds: 10
    failureThreshold: 10
  readinessProbe:
    initialDelaySeconds: 20
    timeoutSeconds: 10
    periodSeconds: 10
    failureThreshold: 10
```

-   **`webserverConfig.stringOverride`**: This is a critical section that injects a Python script into the webserver's configuration. It enables Google OAuth for user authentication, sets the default role for new users to `Viewer`, and provides the OAuth provider details, including the client ID and secret.
-   **`resources`, `affinity`, `tolerations`**: Defines resource limits and specific scheduling rules for the webserver pod, similar to the scheduler.
-   **`livenessProbe` & `readinessProbe`**: Configures Kubernetes health checks to ensure the webserver pod is running and ready to serve traffic.

### Workers (`workers`)

```yaml
workers:
  enabled: false
```

-   **`enabled: false`**: This section is disabled because we are using the `KubernetesExecutor`. With `KubernetesExecutor`, there are no long-running worker pods; instead, pods are created on-demand for each task.

### Triggerer (`triggerer`)

```yaml
triggerer:
  enabled: true
  replicas: 2
  resources:
    limits:
      cpu: 250m
      memory: 500Mi
    requests:
      cpu: 150m
      memory: 300Mi
```

-   **`enabled: true`**: Enables the triggerer component to support deferrable operators.
-   **`replicas`**: Runs two instances of the triggerer for high availability.
-   **`resources`**: Defines the CPU and memory requests and limits for the triggerer pods.

### Flower (`flower`)

```yaml
flower:
  enabled: false
```

-   **`enabled: false`**: Flower is a monitoring tool for Celery, and since we are not using `CeleryExecutor`, it is disabled.

### DAGs (`dags`)

This block configures how DAGs are loaded into Airflow.

```yaml
dags:
  gitSync:
    image:
      repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/git-sync
      tag: v3.6.9
      pullPolicy: IfNotPresent
    enabled: true
    repo: "https://bitbucket.org/ncinga/zilingo-airflow-devops.git"
    repoSubPath: "solvei8_dags"
    branch: madhav
    httpSecret: "airflow-bitbucket-secret"
    httpSecretPasswordKey: "app-password"
```

-   **`gitSync.enabled: true`**: Enables syncing DAGs from a Git repository.
-   **`repo`**: The URL of the Bitbucket repository containing the DAGs.
-   **`repoSubPath`**: Specifies that only the `solvei8_dags` directory from the repository should be synced.
-   **`branch`**: The Git branch to sync from.
-   **`httpSecret` & `httpSecretPasswordKey`**: Specifies the Kubernetes secret and the key within that secret that holds the app password for authenticating with the Bitbucket repository.

### Ingress (`ingress`)

This configures how the Airflow UI is exposed to the internet.

```yaml
ingress:
  enabled: true
  web:
    annotations:
      cert-manager.io/cluster-issuer: letsencrypt-issuer
      nginx.ingress.kubernetes.io/configuration-snippet: |
        more_set_headers "X-Content-Type-Options: nosniff";
        more_set_headers "X-Frame-Options: SAMEORIGIN";
        more_set_headers "Referrer-Policy: strict-origin-when-cross-origin";
        more_set_headers "Strict-Transport-Security: max-age=31536000; includeSubDomains; preload";
        more_set_headers "Cross-Origin-Resource-Policy: same-site";
    host: "airflow.strawmine.com"
    ingressClassName: "external-nginx"
    tls:
      enabled: true
      secretName: "airflow.strawmine.com-tls"
```

-   **`enabled: true`**: Creates a Kubernetes Ingress resource.
-   **`web.annotations`**:
    -   `cert-manager.io/cluster-issuer`: Tells `cert-manager` to automatically provision a TLS certificate from Let's Encrypt.
    -   `nginx.ingress.kubernetes.io/configuration-snippet`: Adds important security headers to the HTTP response.
-   **`web.host`**: The public domain name for the Airflow UI.
-   **`web.ingressClassName`**: Specifies that the `external-nginx` ingress controller should handle this ingress.
-   **`web.tls`**: Enables TLS and specifies the secret where the certificate and key will be stored.

### Service Account (`serviceAccount`)

```yaml
serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/KarpenterControllerRole-staging-solvei8-eks
```

-   **`annotations`**: This annotation links the Kubernetes service account used by Airflow pods to an AWS IAM role. This is known as IAM Roles for Service Accounts (IRSA) and is the secure way to grant Airflow pods AWS permissions (e.g., to access the S3 logging bucket).

### PgBouncer (`pgbouncer`)

```yaml
pgbouncer:
  enabled: true
  image:
    repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/pgbouncer
    tag: 1.22.1-patch.0
    pullPolicy: IfNotPresent
  resources:
    limits:
      cpu: 100m
      memory: 100Mi
    requests:
      cpu: 50m
      memory: 20Mi
  affinity: 
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: "nodepool"
                operator: In
                values:
                  - "staging-solvei8-amd-on-demand-general-purpose"
  tolerations: 
    - key: "workload"
      operator: "Equal"
      value: "ondemand-general"
      effect: "NoSchedule"
```

-   **`enabled: true`**: Deploys PgBouncer to act as a connection pooler for the PostgreSQL database.
-   **`image`, `resources`, `affinity`, `tolerations`**: Defines the Docker image, resource limits, and scheduling rules for the PgBouncer pod(s).

### PostgreSQL (`postgresql`)

This block configures the metadata database.

```yaml
postgresql:
  image:
    registry: ************.dkr.ecr.ap-southeast-1.amazonaws.com
    repository: postgresql-bitnami
    tag: 11.22-patch.0
    pullPolicy: IfNotPresent
  enabled: true
  persistence:
    enabled: true
    storageClass: "ebs-sc"
    accessModes:
      - ReadWriteOnce
    size: 8Gi
  resources:
    limits:
      cpu: 250m
      memory: 256Mi
    requests:
      cpu: 100m
      memory: 100Mi
  master:
    affinity: 
      nodeAffinity:
        requiredDuringSchedulingIgnoredDuringExecution:
          nodeSelectorTerms:
            - matchExpressions:
                - key: "nodepool"
                  operator: In
                  values:
                    - "staging-solvei8-amd-on-demand-general-purpose"
    tolerations: 
      - key: "workload"
        operator: "Equal"
        value: "ondemand-general"
        effect: "NoSchedule"
```

-   **`enabled: true`**: Deploys a PostgreSQL instance as a sub-chart.
-   **`image`**: Specifies the Docker image for PostgreSQL, hosted in a private ECR repository.
-   **`persistence`**: Configures a PersistentVolumeClaim (PVC) to store the database data. This ensures that the data survives pod restarts. It uses the `ebs-sc` storage class and requests 8Gi of storage.
-   **`resources`, `master.affinity`, `master.tolerations`**: Defines resource limits and scheduling rules for the PostgreSQL pod.

### Redis (`redis`)

```yaml
redis:
  enabled: false
```

-   **`enabled: false`**: Redis is not needed because we are not using `CeleryExecutor`, so it is disabled.
